// lib/api.js — compatibility shim
import Constants from "expo-constants";
const EXTRA = (Constants?.expoConfig?.extra) || {};

export const OPENAI_MODEL  = EXTRA.EXPO_PUBLIC_OPENAI_MODEL  || "gpt-4.1-mini";
export const OPENAI_PROXY  = EXTRA.EXPO_PUBLIC_PROXY_URL      || "http://localhost:8788";
export const AVAILABILITY_BASE = EXTRA.EXPO_PUBLIC_AVAILABILITY_API_BASE || "http://localhost:8787";

/**
 * Do NOT `export * from "./ai"` here — it can create a static circular import.
 * Use dynamic imports to forward calls safely.
 */
export async function suggestDomains(args) {
  const m = await import("./ai");
  return m.suggestDomains(args);
}
export async function businessCopy(args) {
  const m = await import("./ai");
  return m.businessCopy(args);
}
export async function buildWebsite(args) {
  const m = await import("./ai");
  return m.buildWebsite(args);
}
export async function generateLogo(args) {
  const m = await import("./ai");
  return m.generateLogo(args);
}
