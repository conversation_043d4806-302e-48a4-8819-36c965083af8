<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/bg_split_curved">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        >

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/ColorPrimaryDark"
            app:title="Edit Site"
            app:titleTextColor="@android:color/white"
            app:navigationIcon="@drawable/ic_back_arrow"/>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:orientation="vertical">

<!--        <TextView-->
<!--            android:id="@+id/edit_site_title"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="Edit Your .KE Website"-->
<!--            android:textSize="24sp"-->
<!--            android:textStyle="bold"-->
<!--            android:textColor="@color/ColorSecondary"-->
<!--            android:layout_marginBottom="16dp" />-->

        <TextView
            android:id="@+id/tv_domain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Editing: Loading..."
            android:textSize="18sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/label_site_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Site Title"
            android:textSize="16sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_site_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter site title (e.g., My Business)"
            android:inputType="text"
            android:padding="12dp"
            android:textColor="@color/black"
            android:textColorHint="@color/grayDark"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/label_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Site Description"
            android:textSize="16sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter a short description"
            android:inputType="textMultiLine"
            android:padding="12dp"
            android:textColorHint="@color/grayDark"
            android:textColor="@color/black"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp"
            android:minLines="3" />

        <TextView
            android:id="@+id/label_hero_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Hero Image URL"
            android:textSize="16sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_hero_image"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter image URL (e.g., https://example.com/image.jpg)"
            android:inputType="textUri"
            android:padding="12dp"
            android:textColorHint="@color/grayDark"
            android:textColor="@color/black"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_preview_changes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Preview Changes"
            android:backgroundTint="#2196F3"
            android:textColor="@color/whiteMore"
            android:background="@drawable/btn_primary_green"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_save_changes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Save Changes"
            android:backgroundTint="#4CAF50"
            android:textColor="#FFFFFF"
            android:background="@drawable/btn_primary_green"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FF0000"
            android:layout_marginTop="8dp"
            android:visibility="gone" />
    </LinearLayout>




    </LinearLayout>
</ScrollView>