<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Base.Theme.OneClick" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Brand → Material -->
        <item name="colorPrimary">@color/ColorSecondary</item>            <!-- buttons default -->
        <item name="colorPrimaryVariant">@color/ColorSecondaryDark</item>
        <item name="colorOnPrimary">@color/white</item>

        <item name="colorSecondary">@color/blueAccent</item>
        <item name="colorSecondaryVariant">@color/tealAccent</item>
        <item name="colorOnSecondary">@color/white</item>

<!--        <item name="colorBackground">@color/ColorBackground</item>-->
        <item name="colorSurface">@color/ColorPrimaryDark</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="android:statusBarColor">@color/ColorPrimaryDark</item>

        <!-- Default button styles -->
        <item name="materialButtonStyle">@style/Widget.OneClick.Button.Filled</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.OneClick.Button.Outlined</item>

        <!-- For older widgets that still read this -->
        <item name="colorAccent">@color/ColorSecondary</item>
    </style>

    <style name="Theme.OneClick" parent="Base.Theme.OneClick"/>

    <!-- Filled button -->
    <style name="Widget.OneClick.Button.Filled" parent="Widget.MaterialComponents.Button">
        <item name="cornerRadius">20dp</item>
        <item name="backgroundTint">@color/ColorSecondary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- Outlined button -->
    <style name="Widget.OneClick.Button.Outlined" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="cornerRadius">20dp</item>
        <item name="strokeColor">@color/ColorSecondary</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/ColorSecondary</item>
    </style>
</resources>
