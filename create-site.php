<?php
header('Content-Type: application/json');

// --- CONFIGURATION ---
$admin_user = 'admin'; // IMPORTANT: Change this
$free_product_id = 1; // The Product ID (PID) of the "Instant Site" product you created.
$payment_method = 'mailinpayment'; // The "dummy" payment gateway.
$base_domain = 'oneclick.me.ke';
$sites_directory = '/var/www/html/sites/'; // We will create this folder.

// --- INITIALIZE WHMCS ---
require_once('../whmcs/init.php');

// --- GET INPUT FROM THE APP (JSON Body) ---
$json_input = file_get_contents('php://input');
$data = json_decode($json_input, true);

// --- VALIDATION ---
if (!$data || !isset($data['user']) || !isset($data['subdomain']) || !isset($data['html_content'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid input data.']);
    exit();
}
$user_info = $data['user'];
$subdomain = preg_replace("/[^a-zA-Z0-9\-]/", "", strtolower($data['subdomain']));
$html_content = $data['html_content'];
$full_domain = $subdomain . '.' . $base_domain;
$site_path = $sites_directory . $full_domain;

if (file_exists($site_path)) {
    echo json_encode(['status' => 'error', 'message' => 'Sorry, that subdomain is already taken.']);
    exit();
}

// --- STEP 1: GET OR CREATE CLIENT IN WHMCS ---
$client_id = null;
$user_email = $user_info['email'];

// First, check if a client with this email already exists
$existing_client_results = localAPI('GetClients', ['search' => $user_email], $admin_user);

if ($existing_client_results['result'] == 'success' && $existing_client_results['totalresults'] > 0) {
    // User exists! Get their Client ID.
    $client_id = $existing_client_results['clients']['client'][0]['id'];
} else {
    // User does not exist, so create a new one.
    // (This is your existing AddClient code)
    $email_parts = explode('@', $user_info['email']);
    $first_name = ucfirst($email_parts[0]);

    $client_postData = [
        'firstname' => $first_name,
        'lastname' => 'OneClick User',
        'email' => $user_info['email'],
        'password2' => $user_info['password'],
        'companyname' => 'OneClick Site',
        'address1' => 'Digital Address',
        'city' => 'Nairobi',
        'state' => 'Nairobi',
        'postcode' => '00100',
        'country' => 'KE',
        'phonenumber' => '**********',
        'skipvalidation' => true,
    ];
    $client_results = localAPI('AddClient', $client_postData, $admin_user);

    if ($client_results['result'] != 'success') {
        echo json_encode(['status' => 'error', 'message' => 'Could not create user account in WHMCS.']);
        exit();
    }
    $client_id = $client_results['clientid'];
}

// --- STEP 2: CREATE & ACCEPT THE FREE ORDER (NO BILLING) ---
$order_results = localAPI('AddOrder', [
    'clientid' => $client_id, 'pid' => [$free_product_id],
    'domain' => [$full_domain], 'billingcycle' => ['free'],
    'paymentmethod' => $payment_method, 'noinvoice' => true, 'noemail' => true,
], $admin_user);
$order_id = $order_results['orderid'];
localAPI('AcceptOrder', ['orderid' => $order_id], $admin_user);

// --- STEP 3: DEPLOY THE WEBSITE ON THE SERVER ---
mkdir($site_path, 0755, true);
file_put_contents($site_path . '/index.html', $html_content);
shell_exec("sudo chown -R www-data:www-data " . escapeshellarg($site_path));

// --- STEP 4: RETURN SUCCESS ---
echo json_encode([
    'status' => 'success',
    'message' => 'Website created successfully!',
    'live_url' => 'https://' . $full_domain
]);
exit();

