package com.capson.oneclick.models;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

public class SharedViewModel extends ViewModel {
    private final MutableLiveData<String> profileImageUrl = new MutableLiveData<>();

    public void setProfileImageUrl(String url) {
        profileImageUrl.setValue(url);
    }

    public LiveData<String> getProfileImageUrl() {
        return profileImageUrl;
    }
}
