// lib/ai.js
import { OPENAI_MODEL, OPENAI_PROXY } from "./api";

async function openaiJson(messages) {
  const res = await fetch(`${OPENAI_PROXY}/v1/responses`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ model: OPENAI_MODEL, messages })
  });

  const raw = await res.text();
  let j = {};
  try { j = JSON.parse(raw); } catch {}
  if (!res.ok) throw new Error(j?.error?.message || `HTTP ${res.status}`);

  const text =
    j.output_text ||
    (j.content && j.content[0] && j.content[0].text) ||
    (j.output && j.output[0]?.content?.[0]?.text) ||
    "";

  try { return JSON.parse(text); } catch { return {}; }
}

export async function suggestDomains({ idea, group }) {
  return openaiJson([
    { role: "system", content: "Return JSON {domains:string[]} of 12 brandable .ke/.co.ke/.me.ke domains. No commentary." },
    { role: "user", content: JSON.stringify({ idea, audience_group: group }) }
  ]);
}

export async function businessCopy({ domain, lang="en", group }) {
  return openaiJson([
    { role: "system", content: "Return JSON {headline, subheadline, bullets:string[]} for landing pages. No commentary." },
    { role: "user", content: JSON.stringify({ domain, lang, audience_group: group }) }
  ]);
}

export async function buildWebsite({ domain, template, theme, pages, group }) {
  return openaiJson([
    { role: "system", content:
      "Return JSON {site:{title,template,brandColor,font,cornerRadius}," +
      " pages:{home,about,contact,products}}. Each page is HTML (body only), Tailwind classes, no comments."
    },
    { role: "user", content: JSON.stringify({ domain, template, theme, pages, audience_group: group }) }
  ]);
}

// Uses server's /v1/logo/generate → returns data URL or remote URL
export async function generateLogo({ prompt }) {
  const res = await fetch(`${OPENAI_PROXY}/v1/logo/generate`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ prompt })
  });
  const j = await res.json().catch(() => ({}));
  if (!res.ok) throw new Error(j?.error || `Logo failed (HTTP ${res.status})`);
  return j.dataUrl || "";
}
