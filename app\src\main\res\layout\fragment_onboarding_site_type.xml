<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:padding="16dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_split_curved">

        <TextView
            android:id="@+id/title_site_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="What type of website do you need?"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/ColorSecondary"
            android:layout_marginBottom="12dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Choose the closest fit."
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="16dp"/>

        <!-- 2-column grid like Explore -->
        <androidx.gridlayout.widget.GridLayout
            android:id="@+id/site_type_grid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:columnCount="2">

            <!-- Online Store -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_store"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="4dp"
                        android:src="@drawable/ic_store"
                        android:contentDescription="Online Store" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Online Store"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Sell products"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Blog -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_blog"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="4dp"
                        android:src="@drawable/ic_blog"
                        android:contentDescription="Blog" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Blog"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Articles and updates"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Portfolio -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_portfolio"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="4dp"
                        android:src="@drawable/ic_portfolio"
                        android:contentDescription="Portfolio" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Portfolio"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Showcase your work"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Personal Profile -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_profile"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="4dp"
                        android:src="@drawable/ic_profile"
                        android:contentDescription="Personal Profile" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Personal Profile"
                        android:textStyle="bold"
                        android:textSize="14sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Simple profile"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </androidx.gridlayout.widget.GridLayout>
    </LinearLayout>
</ScrollView>
