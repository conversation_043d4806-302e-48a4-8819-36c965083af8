<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />




    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:usesCleartextTraffic="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/oneclickmainlogo2"
        android:label="@string/app_name"
        android:roundIcon="@drawable/oneclickmainlogo"
        android:supportsRtl="true"
        android:theme="@style/Theme.OneClick"
        tools:targetApi="31">
        <activity
            android:name=".activities.EmailSetupActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".activities.EditSiteActivity"
            android:exported="false" />
        <activity
            android:name=".activities.PreviewActivity"
            android:exported="false"
        android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.DashboardActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".activities.DomainSetupActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".activities.OnboardingActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".activities.LoginActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".activities.LandingActivity"
            android:exported="false" />
        <activity
            android:name=".activities.SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.MainActivity"
            android:exported="true" />
    </application>

</manifest>