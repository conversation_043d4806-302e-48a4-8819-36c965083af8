import * as FileSystem from "expo-file-system";
import * as Sharing from "expo-sharing";
import J<PERSON><PERSON><PERSON> from "jszip";

export async function exportZip(fileMap){
  const zip = new JSZip();
  Object.entries(fileMap).forEach(([p,c]) => zip.file(p, c));
  const b64 = await zip.generateAsync({ type:"base64" });
  const dest = `${FileSystem.cacheDirectory}oneclick_site.zip`;
  await FileSystem.writeAsStringAsync(dest, b64, { encoding: FileSystem.EncodingType.Base64 });
  try { await Sharing.shareAsync(dest, { mimeType:"application/zip", dialogTitle:"Download ZIP" }); } catch {}
  return dest;
}
