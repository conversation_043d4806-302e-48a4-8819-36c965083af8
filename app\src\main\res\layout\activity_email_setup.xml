<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_split_curved">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/ColorPrimaryDark"
        app:title="Set Up Branded Email"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_back_arrow"/>



    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:orientation="vertical">

<!--        <TextView-->
<!--            android:id="@+id/email_setup_title"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="Set Up Branded Email"-->
<!--            android:textSize="24sp"-->
<!--            android:textStyle="bold"-->
<!--            android:textColor="@color/ColorSecondary"-->
<!--            android:layout_marginBottom="16dp" />-->

        <TextView
            android:id="@+id/tv_domain"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Domain: Loading..."
            android:textSize="18sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/label_email_prefix"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Email Prefix (e.g., info)"
            android:textSize="16sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="8dp" />

        <EditText
            android:id="@+id/et_email_prefix"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Enter prefix (e.g., info, hello)"
            android:inputType="text"
            android:padding="12dp"
            android:textColorHint="@color/grayDark"
            android:textColor="@color/black"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_preview_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Your email: <EMAIL>"
            android:textSize="16sp"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_pay_mpesa"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Pay for Premium (M-PESA)"
            android:backgroundTint="#2196F3"
            android:textColor="#FFFFFF"
            android:background="@drawable/btn_primary_green"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_setup_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Set Up Email"
            android:backgroundTint="#4CAF50"
            android:textColor="#FFFFFF"
            android:background="@drawable/btn_primary_green"
            android:layout_marginBottom="16dp"
            android:enabled="false" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#FF0000"
            android:layout_marginTop="8dp"
            android:visibility="gone" />

    </LinearLayout>



</LinearLayout>