package com.capson.oneclick.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.viewpager2.widget.ViewPager2;

import com.capson.oneclick.R;
import com.capson.oneclick.TestConfig;
import com.capson.oneclick.adapters.OnboardingPagerAdapter;
import com.capson.oneclick.fragments.OnboardingIdentityFragment;
import com.capson.oneclick.fragments.OnboardingSiteTypeFragment;


public class OnboardingActivity extends AppCompatActivity
        implements OnboardingIdentityFragment.Listener, OnboardingSiteTypeFragment.Listener {

    private ViewPager2 viewPager;
    private Button btnBack, btnNext;

    private String selectedIdentity = null;
    private String selectedWebsiteType = null;
    private boolean testMode = false; // ★

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_onboarding);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());

        testMode = getIntent().getBooleanExtra("testMode", false);

        // Fragments with callback into this activity
        OnboardingIdentityFragment f1 = new OnboardingIdentityFragment(this);
        OnboardingSiteTypeFragment f2 = new OnboardingSiteTypeFragment(this);

        viewPager = findViewById(R.id.viewPager);
        viewPager.setUserInputEnabled(false); // lock swipe; we control with Next/Back
        viewPager.setAdapter(new OnboardingPagerAdapter(this, f1, f2));

        btnBack = findViewById(R.id.btn_back);
        btnNext = findViewById(R.id.btn_next);

        btnBack.setOnClickListener(v -> {
            int pos = viewPager.getCurrentItem();
            if (pos > 0) {
                viewPager.setCurrentItem(pos - 1, true);
                updateButtons();
            }
        });

        btnNext.setOnClickListener(v -> {
            int pos = viewPager.getCurrentItem();
            if (pos == 0) {
                // next to step 2 only if identity chosen
                if (selectedIdentity == null) return;
                viewPager.setCurrentItem(1, true);
                updateButtons();
            } else {
                // finish only if website type chosen
                if (selectedWebsiteType == null) return;

                Intent intent = new Intent(this, DomainSetupActivity.class);
                intent.putExtra("identity", selectedIdentity);
                intent.putExtra("website_type", selectedWebsiteType);
                intent.putExtra("testMode", testMode); // ★
                startActivity(intent);
            }
        });

        // ★ Optional: test defaults (still editable)
        if (testMode) {
            selectedIdentity = TestConfig.TEST_IDENTITY;        // e.g., "Small Business (SME)"
            selectedWebsiteType = TestConfig.TEST_WEBSITE_TYPE;  // e.g., "Portfolio"
        }

        updateButtons();
    }

    private void updateButtons() {
        int pos = viewPager.getCurrentItem();
        btnBack.setEnabled(pos > 0);
        if (pos == 0) {
            btnNext.setText(selectedIdentity == null ? "Next" : "Next");
            btnNext.setEnabled(selectedIdentity != null);
        } else {
            btnNext.setText("Continue");
            btnNext.setEnabled(selectedWebsiteType != null);
        }
    }

    // Callbacks from fragments
    @Override
    public void onIdentitySelected(String identity) {
        selectedIdentity = identity;
        updateButtons();
    }

    @Override
    public void onWebsiteTypeSelected(String type) {
        selectedWebsiteType = type;
        updateButtons();
    }
}
