package com.capson.oneclick.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import com.capson.oneclick.R;

public class DashboardActivity extends AppCompatActivity {

    private TextView tvDomain, tvWebsite, tvRenewal;
    private Button btnViewSite, btnEditSite, btnRenewMpesa, btnSetupEmail;

    private String domain;
    private String websiteType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // Make sure your layout is the ScrollView version we created: activity_dashboard.xml
        setContentView(R.layout.activity_dashboard);

        tvDomain       = findViewById(R.id.tv_domain);
        tvWebsite      = findViewById(R.id.tv_website);
        tvRenewal      = findViewById(R.id.tv_renewal);
        btnViewSite    = findViewById(R.id.btn_view_site);
        btnEditSite    = findViewById(R.id.btn_edit_site);
        btnRenewMpesa  = findViewById(R.id.btn_renew_mpesa);
        btnSetupEmail  = findViewById(R.id.btn_setup_email);

        // Pull extras (can be null)
        Intent intent = getIntent();
        domain = safe(intent.getStringExtra("domain"));
        websiteType = safe(intent.getStringExtra("website_type"));

        // --- Always make the key items visible ---
        btnViewSite.setVisibility(View.VISIBLE);
        btnEditSite.setVisibility(View.VISIBLE);
        tvRenewal.setVisibility(View.VISIBLE);

        // --- Fill UI with either real data or friendly placeholders ---
        if (!domain.isEmpty()) {
            tvDomain.setText(domain);
            tvWebsite.setText((websiteType.isEmpty() ? "Starter" : websiteType) + " Website: " + domain);
        } else {
            tvDomain.setText("No domains registered");
            tvWebsite.setText("No websites deployed");
        }

        // Renewal logic: show M-PESA only when there’s something to renew AND we have a domain
        boolean renewalDue = checkRenewalStatus(domain);
        if (renewalDue && !domain.isEmpty()) {
            tvRenewal.setText("Renewal due in 30 days");
            btnRenewMpesa.setVisibility(View.VISIBLE);
        } else {
            tvRenewal.setText("No renewals due");
            btnRenewMpesa.setVisibility(View.GONE);
        }

        // --- Clicks ---
        btnViewSite.setOnClickListener(v -> {
            Intent previewIntent = new Intent(DashboardActivity.this, PreviewActivity.class);
            previewIntent.putExtra("domain", domain);
            previewIntent.putExtra("website_type", websiteType);
            startActivity(previewIntent);
        });

        btnEditSite.setOnClickListener(v -> {
            Intent editIntent = new Intent(DashboardActivity.this, EditSiteActivity.class);
            editIntent.putExtra("domain", domain);
            editIntent.putExtra("website_type", websiteType);
            startActivity(editIntent);
        });

        btnRenewMpesa.setOnClickListener(v -> {
            if (processMpesaRenewal(domain)) {
                tvRenewal.setText("Renewal successful! No renewals due.");
                btnRenewMpesa.setVisibility(View.GONE);
            } else {
                tvRenewal.setText("Renewal payment failed. Try again.");
            }
        });

        btnSetupEmail.setOnClickListener(v -> {
            Intent emailIntent = new Intent(DashboardActivity.this, EmailSetupActivity.class);
            emailIntent.putExtra("domain", domain);
            startActivity(emailIntent);
        });
    }

    private String safe(String s) {
        return s == null ? "" : s.trim();
    }

    private boolean checkRenewalStatus(String domain) {
        // TODO: Replace with actual renewal status check
        // Simulate renewal due only if we actually have a domain
        return !domain.isEmpty();
    }

    private boolean processMpesaRenewal(String domain) {
        // TODO: Replace with real M-PESA integration
        return !domain.isEmpty();
    }
}
