<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:padding="16dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_split_curved">

        <TextView
            android:id="@+id/title_identity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Who are you?"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/ColorSecondary"
            android:layout_marginBottom="12dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Pick one to tailor your setup."
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="16dp"/>

        <!-- 2-column grid like Explore -->
        <androidx.gridlayout.widget.GridLayout
            android:id="@+id/identity_grid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:columnCount="2">

            <!-- SME -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_sme"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="4dp"
                        android:src="@drawable/ic_business"
                        android:contentDescription="SME" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Small Business"
                        android:textStyle="bold"
                        android:textSize="12sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Run shop/service"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Creator -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_creator"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="12dp"
                        android:src="@drawable/ic_user"
                        android:contentDescription="Creator" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Creator"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Artist, writer etc"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Student -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_student"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="12dp"
                        android:src="@drawable/ic_student"
                        android:contentDescription="Student" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Student"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Portfolio site"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Other -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_other"
                android:layout_width="0dp"
                android:layout_height="160dp"
                android:layout_margin="0dp"
                app:layout_columnWeight="1"
                app:cardCornerRadius="10dp"
                app:cardElevation="9dp"
                app:cardUseCompatPadding="true"
                app:cardBackgroundColor="@color/white"
                app:strokeColor="@android:color/transparent"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="8dp">

                    <ImageView
                        android:layout_width="60dp"
                        android:layout_height="50dp"
                        android:layout_gravity="center"
                        android:padding="12dp"
                        android:src="@drawable/ic_other"
                        android:contentDescription="Other" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Other"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:layout_marginTop="0dp"/>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_margin="6dp"
                        android:background="@android:color/darker_gray" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="other"
                        android:textColor="@color/grayDark"
                        android:textSize="12sp"
                        android:layout_gravity="center" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </androidx.gridlayout.widget.GridLayout>
    </LinearLayout>
</ScrollView>
