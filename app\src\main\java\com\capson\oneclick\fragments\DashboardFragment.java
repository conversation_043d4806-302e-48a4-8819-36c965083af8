package com.capson.oneclick.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.capson.oneclick.R;
import com.capson.oneclick.TestConfig;
import com.capson.oneclick.activities.EditSiteActivity;
import com.capson.oneclick.activities.EmailSetupActivity;

public class DashboardFragment extends Fragment {

    private TextView tvDomain, tvWebsite, tvRenewal;
    private Button btnViewSite, btnEditSite, btnSetupEmail;

    private String domain = "";
    private String websiteType = "";
    private boolean testMode = false;

    public static DashboardFragment newInstance(String domain, String websiteType) {
        DashboardFragment f = new DashboardFragment();
        Bundle args = new Bundle();
        args.putString("domain", domain);
        args.putString("website_type", websiteType);
        f.setArguments(args);
        return f;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        // Make sure this matches your actual XML filename
        return inflater.inflate(R.layout.fragment_dashboard, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View v, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(v, savedInstanceState);

        tvDomain     = v.findViewById(R.id.tv_domain);
        tvWebsite    = v.findViewById(R.id.tv_website);
        tvRenewal    = v.findViewById(R.id.tv_renewal);   // may be null if not in layout
        btnViewSite  = v.findViewById(R.id.btn_view_site);
        btnEditSite  = v.findViewById(R.id.btn_edit_site);
        btnSetupEmail= v.findViewById(R.id.btn_setup_email);

        // Get args or Activity extras
        if (getArguments() != null) {
            domain = safe(getArguments().getString("domain"));
            websiteType = safe(getArguments().getString("website_type"));
        }
        if (TextUtils.isEmpty(domain) && getActivity() != null && getActivity().getIntent() != null) {
            String d  = safe(getActivity().getIntent().getStringExtra("domain"));
            String wt = safe(getActivity().getIntent().getStringExtra("website_type"));
            if (!d.isEmpty()) domain = d;
            if (!wt.isEmpty()) websiteType = wt;
            testMode = getActivity().getIntent().getBooleanExtra("testMode", TestConfig.TEST_MODE);
        } else {
            testMode = TestConfig.TEST_MODE;
        }

        if (testMode && TextUtils.isEmpty(domain)) {
            domain = TestConfig.TEST_DOMAIN != null ? TestConfig.TEST_DOMAIN : "mybusiness.co.ke";
            websiteType = TextUtils.isEmpty(websiteType)
                    ? (TestConfig.TEST_WEBSITE_TYPE != null ? TestConfig.TEST_WEBSITE_TYPE : "Portfolio")
                    : websiteType;
        }

        // Clicks
        safeSetOnClick(btnViewSite, v1 -> openPreviewFragment(domain, websiteType, testMode));
        safeSetOnClick(btnEditSite, v12 -> {
            Intent i = new Intent(requireContext(), EditSiteActivity.class);
            i.putExtra("domain", domain);
            i.putExtra("website_type", websiteType);
            i.putExtra("testMode", testMode);
            if (getActivity() != null && getActivity().getIntent() != null) {
                String logoDataUrl = getActivity().getIntent().getStringExtra("logo_data_url");
                if (logoDataUrl != null) i.putExtra("logo_data_url", logoDataUrl);
            }
            startActivity(i);
        });
        safeSetOnClick(btnSetupEmail, v13 -> {
            Intent i = new Intent(requireContext(), EmailSetupActivity.class);
            i.putExtra("domain", domain);
            i.putExtra("testMode", testMode);
            startActivity(i);
        });

        updateUI();
    }

    private void updateUI() {
        // Null-safe visibility calls (won’t crash if views are missing)
        safeSetVisible(btnViewSite, View.VISIBLE);
        safeSetVisible(btnEditSite, View.VISIBLE);
        safeSetVisible(tvRenewal, View.GONE);   // hide by default; change as needed

        if (!TextUtils.isEmpty(domain)) {
            safeSetText(tvDomain, domain);
            safeSetText(tvWebsite,
                    (TextUtils.isEmpty(websiteType) ? "Starter" : websiteType) + " Website: " + domain);
        } else {
            safeSetText(tvDomain, "No domains registered");
            safeSetText(tvWebsite, "No websites deployed");
        }
    }

    /* ----------------- helpers ----------------- */

    private static String safe(String s) { return s == null ? "" : s.trim(); }

    private static void safeSetText(TextView v, String text) {
        if (v == null) return;
        v.setText(text);
        v.setVisibility(View.VISIBLE);
    }

    private static void safeSetVisible(View v, int visibility) {
        if (v == null) return;
        v.setVisibility(visibility);
    }

    private static void safeSetOnClick(View v, View.OnClickListener l) {
        if (v == null) return;
        v.setOnClickListener(l);
    }

    private void openPreviewFragment(@NonNull String domain, @NonNull String websiteType, boolean testMode) {
        if (domain.trim().isEmpty()) return;

        com.capson.oneclick.fragments.PreviewFragment preview =
                com.capson.oneclick.fragments.PreviewFragment.newInstance(
                        domain, websiteType, null, null, null);

        Bundle args = preview.getArguments() != null ? preview.getArguments() : new Bundle();
        args.putBoolean("testMode", testMode);
        if (getActivity() != null && getActivity().getIntent() != null) {
            String filesJson  = getActivity().getIntent().getStringExtra("site_files_json");
            String logoDataUrl= getActivity().getIntent().getStringExtra("logo_data_url");
            if (filesJson != null) args.putString("site_files_json", filesJson);
            if (logoDataUrl != null) args.putString("logo_data_url", logoDataUrl);
        }
        preview.setArguments(args);

        requireActivity()
                .getSupportFragmentManager()
                .beginTransaction()
                .setReorderingAllowed(true)
                .setCustomAnimations(
                        android.R.anim.slide_in_left,
                        android.R.anim.fade_out,
                        android.R.anim.fade_in,
                        android.R.anim.slide_out_right)
                .replace(R.id.fragment_container, preview, "PreviewFragment")
                .addToBackStack("Preview")
                .commit();
    }
}
