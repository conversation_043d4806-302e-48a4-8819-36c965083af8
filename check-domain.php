<?php
header('Content-Type: application/json');

// --- CONFIGURATION ---
$admin_user = 'admin'; // IMPORTANT: Change this

// --- INITIALIZE WHMCS ---
require_once('../whmcs/init.php');

// --- INPUT VALIDATION ---
$domain_to_check = isset($_GET['domain']) ? trim($_GET['domain']) : '';
if (empty($domain_to_check)) {
    echo json_encode(['status' => 'error', 'message' => 'Domain name not provided.']);
    exit();
}

// --- WHMCS API COMMAND ---
$command = 'DomainWhois';
$postData = ['domain' => $domain_to_check];
$results = localAPI($command, $postData, $admin_user);

// --- PROCESS & RETURN RESULTS ---
if ($results['result'] == 'success') {
    $is_available = ($results['status'] == 'available');
    $price = 'N/A'; // Default price

    // You can add logic here to get real pricing, but for the hackathon, this is fine.
    if (strpos($domain_to_check, '.co.ke') !== false) {
         $price = '1200.00 KES';
    } else if (strpos($domain_to_check, '.ke') !== false) {
         $price = '3000.00 KES';
    }

    echo json_encode([
        'status' => 'success',
        'domain' => $domain_to_check,
        'available' => $is_available,
        'price' => $price
    ]);
} else {
    echo json_encode(['status' => 'error', 'message' => 'API Error: ' . $results['message']]);
}
exit();
