<?php
header('Content-Type: application/json');

// --- CONFIGURATION ---
$admin_user = 'oneclick_admin';
$free_product_id = 1;
$payment_method = 'mailinpayment';
$base_domain = 'oneclick.me.ke';
$sites_directory = '/var/www/html/sites/';

// --- INITIALIZE WHMCS ---
require_once('../whmcs/init.php');

// --- GET INPUT FROM THE APP (Form Data) ---
// We are no longer expecting JSON. We expect POST fields and a file upload.
$user_email = isset($_POST['email']) ? $_POST['email'] : '';
$user_password = isset($_POST['password']) ? $_POST['password'] : '';
$user_name = isset($_POST['name']) ? $_POST['name'] : 'User';
$subdomain = isset($_POST['subdomain']) ? preg_replace("/[^a-zA-Z0-9\-]/", "", strtolower($_POST['subdomain'])) : '';
$uploaded_file = isset($_FILES['website_zip']) ? $_FILES['website_zip'] : null;

// --- VALIDATION ---
if (empty($user_email) || empty($subdomain) || $uploaded_file === null || $uploaded_file['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid input. Required fields: email, subdomain, and a valid website_zip file.']);
    exit();
}
$full_domain = $subdomain . '.' . $base_domain;
$site_path = $sites_directory . $full_domain;

if (file_exists($site_path)) {
    echo json_encode(['status' => 'error', 'message' => 'Sorry, that subdomain is already taken.']);
    exit();
}

// --- WHMCS CLIENT & ORDER LOGIC (This part is unchanged) ---
// ... (Your existing GetClients/AddClient and AddOrder/AcceptOrder logic goes here) ...
// --- Assume you get a valid $client_id from this logic ---
$client_results = localAPI('GetClients', ['search' => $user_email], $admin_user);
if ($client_results['result'] == 'success' && $client_results['totalresults'] > 0) {
    $client_id = $client_results['clients']['client'][0]['id'];
} else {
    $client_postData = [ 'firstname' => $user_name, 'lastname' => 'OneClick User', 'email' => $user_email, /* ... other fields ... */ 'password2' => $user_password, 'skipvalidation' => true ];
    $new_client_results = localAPI('AddClient', $client_postData, $admin_user);
    if ($new_client_results['result'] != 'success') { echo json_encode(['status' => 'error', 'message' => 'Could not create WHMCS client.']); exit(); }
    $client_id = $new_client_results['clientid'];
}
$order_results = localAPI('AddOrder', ['clientid' => $client_id, 'pid' => [$free_product_id], 'domain' => [$full_domain], 'paymentmethod' => $payment_method, 'noinvoice' => true, 'noemail' => true ], $admin_user);
$order_id = $order_results['orderid'];
localAPI('AcceptOrder', ['orderid' => $order_id], $admin_user);


// --- STEP 3: DEPLOY THE WEBSITE BY UNZIPPING ---
mkdir($site_path, 0755, true);

$zip = new ZipArchive;
if ($zip->open($uploaded_file['tmp_name']) === TRUE) {
    $zip->extractTo($site_path);
    $zip->close();
    
    // Set correct permissions for all the new files
    shell_exec("sudo chown -R www-data:www-data " . escapeshellarg($site_path));
    
    // --- STEP 4: RETURN SUCCESS ---
    echo json_encode([
        'status' => 'success',
        'message' => 'Website deployed successfully!',
        'live_url' => 'https://' . $full_domain
    ]);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Failed to unzip the website archive.']);
}
exit();