package com.capson.oneclick.activities;

import android.Manifest;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBarDrawerToggle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;


import com.capson.oneclick.R;
import com.capson.oneclick.fragments.DashboardFragment;
import com.capson.oneclick.fragments.PreviewFragment;
import com.capson.oneclick.fragments.ProfileFragment;
import com.capson.oneclick.fragments.SettingsFragment;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.bottomnavigation.BottomNavigationView;
// Import Firebase Database


public class MainActivity extends AppCompatActivity {

    //    private DrawerLayout drawerLayout;
//    private NavigationView navigationView;
    private BottomNavigationView bottomNavigationView;
    private Toolbar toolbar;
    private ActionBarDrawerToggle toggle;

//    private MutableLiveData<List<AlertModel>> alertsLiveData = new MutableLiveData<>();
//    private FirebaseFirestore db;
//


    private ImageView navProfileImage;
    private TextView navUserName, navUserEmail;

//    private SharedViewModel sharedViewModel;

    private static final int PERMISSION_REQUEST_CODE = 100;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);


        // Initialize UI Components
//        drawerLayout = findViewById(R.id.drawer_layout);
//        navigationView = findViewById(R.id.navigation_view);
        toolbar = findViewById(R.id.toolbar);
        // Toolbar setup
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());
        bottomNavigationView = findViewById(R.id.bottom_navigation);


        // Initialize navigation header
////        View headerView = navigationView.getHeaderView(0);
//        navProfileImage = headerView.findViewById(R.id.nav_profile_image);
//        navUserName = headerView.findViewById(R.id.nav_username);
//        navUserEmail = headerView.findViewById(R.id.nav_user_email);


//        sharedViewModel = new ViewModelProvider(this).get(SharedViewModel.class);
//
//        sharedViewModel.getProfileImageUrl().observe(this, imageUrl -> {
//            if (imageUrl != null && !imageUrl.isEmpty()) {
//                Picasso.get().load(imageUrl).into(navProfileImage);
//            }
//        });

//        MaterialToolbar toolbar = findViewById(R.id.toolbar);
//        toolbar.setOnMenuItemClickListener(item -> {
//            if (item.getItemId() == R.id.action_notifications) {
//                // Handle click (e.g., open notifications fragment/activity)
//                Toast.makeText(this, "Notifications clicked", Toast.LENGTH_SHORT).show();
//                return true;
//            }
//            return false;
//        });


        // Retrieve the user role from SharedPreferences (Default to "user" if not set)
        SharedPreferences sharedPreferences = getSharedPreferences("user_profile", Context.MODE_PRIVATE);
        String userRole = sharedPreferences.getString("role", "user"); // Default to "user" if no role is set
        Log.d("USER_ROLE", "User role from SharedPreferences: " + userRole);

        // Set up toolbar
        setSupportActionBar(toolbar);

        //db
//        // Initialize Firebase Database
//        FirebaseDatabase database = FirebaseDatabase.getInstance();
//        DatabaseReference myRef = database.getReference("data_readings");
//        db = FirebaseFirestore.getInstance();
////        fetchAlerts();

        // Load profile data into the navigation drawer
//        loadNavHeader();

//        checkAndUpdateUserFields(); // Runs once when the app starts


        // Request all permissions
        requestAllPermissions();

        // Start the monitoring service
//    Intent serviceIntent = new Intent(this, SensorMonitoringService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED ||
                    ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_BACKGROUND_LOCATION) != PackageManager.PERMISSION_GRANTED) {

                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.ACCESS_BACKGROUND_LOCATION
                }, 1001);
            } else {
//                startSensorService();
            }
        } else {
//            startSensorService();
        }

//
//        // Set up Drawer Navigation Toggle
//        toggle = new ActionBarDrawerToggle(this, drawerLayout, toolbar,
//                R.string.navigation_drawer_open, R.string.navigation_drawer_close);
////        drawerLayout.addDrawerListener(toggle);
//        toggle.syncState();

        // Load the default fragment based on the user's role
        if (savedInstanceState == null) {
            loadFragment(new DashboardFragment(), "Dashboard"); // UserDashboardFragment for regular users
        }

//
//        if (userRole.equals("admin")) {
//            navigationView.getMenu().setGroupVisible(R.id.admin_group, true);
//        }


        // Handle Bottom Navigation Clicks
        bottomNavigationView.setOnItemSelectedListener(item -> {
            if (item.getItemId() == R.id.nav_home) {
                loadFragment(new DashboardFragment(), "Dashboard");
//            }
//            else if (item.getItemId() == R.id.nav_profile) {
//                loadFragment(new ProfileFragment(), "Profile");
//            } else if (item.getItemId() == R.id.nav_settings) {
//                loadFragment(new SettingsFragment(), "Settings");
            } else if (item.getItemId() == R.id.nav_preview) {
            loadFragment(new PreviewFragment(), "Website Preview");
        }
            return true;
        });

//        // Handle Side Navigation Clicks
//        navigationView.setNavigationItemSelectedListener(item -> {
//            int id = item.getItemId();
////            if (id == R.id.nav_home) {
////                loadFragment(new DashboardFragment(), "Dashboard");
////            } else if (id == R.id.nav_alerts) {
////                loadFragment(new InboxFragment(), "Inbox");
////            } else
//            if (id == R.id.nav_settings) {
//                loadFragment(new SettingsFragment(), "Settings");
//            } else if (id == R.id.nav_updates) {
//                loadFragment(new UpdatesFragment(), "Updates");
//            } else if (id == R.id.nav_help) {
//                loadFragment(new HelpFragment(), "Help & Support");
//            }

//            else if (id == R.id.nav_user_management) {
//                loadFragment(new UserManagementFragment(), "User Management");
//            }  else if (id == R.id.nav_sensor_management) {
//                loadFragment(new SensorManagementFragment(), "Sensor Management");
//            }
//            else if (id == R.id.nav_logout) {
//                finish(); // Closes the app (Logout)
//            }
//            drawerLayout.closeDrawers(); // Close drawer after selection
//            return true;
//        });
//    }


//    @Override
//    public boolean onCreateOptionsMenu(Menu menu) {
//        // Inflate the menu
//        getMenuInflater().inflate(R.menu.nav_menu, menu);
//
//        // Get the menu item by its ID (ensure it matches the ID in the XML file)
//        MenuItem item = menu.findItem(R.id.nav_sensor_management);
//        MenuItem item2 = menu.findItem(R.id.nav_user_management);
//
//        // Ensure the item is not null before modifying
//        if (item != null) {
//            item.setVisible(true); // or false based on your conditions
//        }
//
//        if (item2 != null) {
//            item2.setVisible(true); // or false based on your conditions
//        }
//
//        return true;
//    }


//    private void loadNavHeader() {
//        FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
//        if (user != null) {
//            db.collection("Users").document(user.getUid())
//                    .get()
//                    .addOnSuccessListener(documentSnapshot -> {
//                        if (documentSnapshot.exists()) {
//                            String name = documentSnapshot.getString("name");
//                            String email = documentSnapshot.getString("email");
//                            String imageUrl = documentSnapshot.getString("profileImage");
//
//                            navUserName.setText(name != null ? name : "User");
//                            navUserEmail.setText(email != null ? email : "Email");
//
////                            if (imageUrl != null && !imageUrl.isEmpty()) {
////                                Picasso.get().load(imageUrl).into(navProfileImage);
////                            }
//                        }
//                    })
//                    .addOnFailureListener(e -> Toast.makeText(this, "Error loading profile", Toast.LENGTH_SHORT).show());
//        }
    }


    private void requestAllPermissions() {
        String[] permissions = {
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.INTERNET,
                Manifest.permission.ACCESS_NETWORK_STATE,
                Manifest.permission.CAMERA,
                Manifest.permission.WRITE_EXTERNAL_STORAGE,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.POST_NOTIFICATIONS
        };

        boolean allPermissionsGranted = true;

        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allPermissionsGranted = false;
                break;
            }
        }

        if (!allPermissionsGranted) {
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allGranted = true;

            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (allGranted) {
                Toast.makeText(this, "All permissions granted!", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Some permissions were denied. The app may not function properly.", Toast.LENGTH_LONG).show();
            }
        }
    }


    // Function to load fragments and update toolbar title
    private void loadFragment(Fragment fragment, String title) {
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit();

        // Update the toolbar title
        toolbar.setTitle(title);
//
//        // Ensure the menu icon (☰) always appears
//        getSupportActionBar().setDisplayHomeAsUpEnabled(false);
//        toggle.setDrawerIndicatorEnabled(true);
//        toggle.syncState();
    }

//    @Override
//    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//        if (requestCode == 1001) {
//            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
//                startSensorService();
//            } else {
//                Toast.makeText(this, "Location permissions required!", Toast.LENGTH_SHORT).show();
//            }
//        }
//    }


//    private void startSensorService() {
//        Intent serviceIntent = new Intent(this, SensorMonitoringService.class);
//        ContextCompat.startForegroundService(this, serviceIntent);
//    }

//    public LiveData<List<AlertModel>> getAlertsLiveData() {
//        return alertsLiveData;
//    }
//    private void fetchAlerts() {
//        db.collection("Alerts").orderBy("timestamp", Query.Direction.DESCENDING)
//                .addSnapshotListener((value, error) -> {
//                    if (error != null) {
//                        Log.e("Firebase", "Error fetching alerts", error);
//                        return;
//                    }
//
//                    List<AlertModel> alertsList = new ArrayList<>();
//                    for (DocumentSnapshot doc : value.getDocuments()) {
//                        AlertModel alert = doc.toObject(AlertModel.class);
//                        alertsList.add(alert);
//                    }
//                    alertsLiveData.setValue(alertsList);
//                });
//    }

//    private void checkAndUpdateUserFields() {
//        FirebaseFirestore db = FirebaseFirestore.getInstance();
//
//        db.collection("Users").get()
//                .addOnSuccessListener(queryDocumentSnapshots -> {
//                    for (DocumentSnapshot doc : queryDocumentSnapshots) {
//                        String userId = doc.getId();
//                        Map<String, Object> updates = new HashMap<>();
//
//                        if (!doc.contains("phoneNumber")) {
//                            updates.put("phoneNumber", "");
//                        }
//
//                        if (!doc.contains("profileImage")) {
//                            updates.put("profileImage", "");
//                        }
//
//                        if (!updates.isEmpty()) {
//                            db.collection("Users").document(userId)
//                                    .set(updates, SetOptions.merge())  // Merge new fields
//                                    .addOnSuccessListener(aVoid ->
//                                            Log.d("Firestore", "Fields updated for user: " + userId))
//                                    .addOnFailureListener(e ->
//                                            Log.e("Firestore", "Error updating user: " + userId, e));
//                        }
//                    }
//                })
//                .addOnFailureListener(e ->
//                        Log.e("Firestore", "Error fetching users", e));
//    }

}



