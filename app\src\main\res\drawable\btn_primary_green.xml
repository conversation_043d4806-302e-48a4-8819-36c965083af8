<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#33FFFFFF">  <!-- subtle light ripple -->

    <!-- Mask ensures rounded ripple -->
    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <corners android:radius="12dp"/>
            <solid android:color="#FFFFFFFF"/>
        </shape>
    </item>

    <!-- Background with states -->
    <item>
        <selector xmlns:android="http://schemas.android.com/apk/res/android">
            <!-- Disabled -->
            <item android:state_enabled="false">
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="#1F2A46"/> <!-- muted navy -->
                </shape>
            </item>

            <!-- Pressed -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="#22880F"/> <!-- darker green -->
                </shape>
            </item>

            <!-- Default -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="#33C51C"/> <!-- ColorSecondary -->
                </shape>
            </item>
        </selector>
    </item>
</ripple>
