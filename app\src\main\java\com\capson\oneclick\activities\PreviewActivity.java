package com.capson.oneclick.activities;

import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Log;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;

import com.capson.oneclick.R;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class PreviewActivity extends AppCompatActivity {

    private static final String API_BASE = "https://oneclick.me.ke/api/";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final String TAG = "PreviewActivity";

    private TextView tvDomain, tvStatus;
    private WebView webViewPreview;
    private Button btnEditSite, btnLaunchSite;
    private String domain, websiteType, htmlContent, businessName, userEmail, userName, userPassword, logoDataUrl;

    private final OkHttpClient client = new OkHttpClient();
    private final Gson gson = new Gson();
    private FirebaseAuth mAuth;
    private DatabaseReference db;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preview);

        // Initialize Firebase
        mAuth = FirebaseAuth.getInstance();
        db = FirebaseDatabase.getInstance().getReference();

        // Toolbar setup
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());

        tvDomain = findViewById(R.id.tv_domain);
        tvStatus = findViewById(R.id.tv_status);
        webViewPreview = findViewById(R.id.webview_preview);
        btnEditSite = findViewById(R.id.btn_edit_site); // May be null if commented out in XML
        btnLaunchSite = findViewById(R.id.btn_launch_site);

        Intent intent = getIntent();
        domain = intent.getStringExtra("domain");
        websiteType = intent.getStringExtra("website_type");
        htmlContent = intent.getStringExtra("html_content");
        businessName = intent.getStringExtra("business_name");
        userEmail = intent.getStringExtra("user_email");
        userName = intent.getStringExtra("user_name");
        userPassword = intent.getStringExtra("user_password");
        logoDataUrl = intent.getStringExtra("logo_data_url");

        if (domain != null && !domain.isEmpty()) {
            tvDomain.setText("Previewing: " + domain);
        } else {
            tvDomain.setText("No domain selected");
            tvStatus.setText("Error: No domain available for preview");
            tvStatus.setVisibility(TextView.VISIBLE);
            return;
        }

        WebSettings webSettings = webViewPreview.getSettings();
        webSettings.setJavaScriptEnabled(true);

        webViewPreview.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                tvStatus.setText("Preview loaded successfully");
                tvStatus.setTextColor(ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_green_dark));
                tvStatus.setVisibility(TextView.VISIBLE);
            }

            // Old signature (pre-API 23) kept for compatibility
            @Override
            @SuppressWarnings("deprecation")
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                tvStatus.setText("Error loading preview: " + description);
                tvStatus.setVisibility(TextView.VISIBLE);
            }

            // New signature (API 23+)
            @Override
            @RequiresApi(23)
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                tvStatus.setText("Error loading preview");
                tvStatus.setVisibility(TextView.VISIBLE);
            }
        });

        // Check if we have HTML content from AI generation
        if (htmlContent != null && !htmlContent.isEmpty()) {
            // Load the AI-generated HTML directly
            webViewPreview.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null);
            tvStatus.setText("AI-generated website preview loaded");
            tvStatus.setTextColor(ContextCompat.getColor(this, android.R.color.holo_green_dark));
            tvStatus.setVisibility(TextView.VISIBLE);

            // Change button text to "Publish My Website"
            btnLaunchSite.setText("Publish My Website");
        } else {
            // Fallback to old preview method
            loadWebsitePreview(domain, websiteType);
        }

        btnEditSite.setOnClickListener(v -> {
            Intent editIntent = new Intent(PreviewActivity.this, EditSiteActivity.class);
            editIntent.putExtra("domain", domain);
            editIntent.putExtra("website_type", websiteType);
            startActivity(editIntent);
        });

        btnLaunchSite.setOnClickListener(v -> {
            if (htmlContent != null && !htmlContent.isEmpty()) {
                // New flow: Publish the AI-generated website
                publishWebsite();
            } else {
                // Old flow: Launch website (fallback)
                if (launchWebsite(domain)) {
                    tvStatus.setText("Website launched successfully: " + domain);
                    tvStatus.setTextColor(
                            ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_green_dark));
                    tvStatus.setVisibility(TextView.VISIBLE);

                    Intent dashboardIntent = new Intent(PreviewActivity.this, DashboardActivity.class);
                    dashboardIntent.putExtra("domain", domain);
                    dashboardIntent.putExtra("website_type", websiteType);
                    startActivity(dashboardIntent);
                    finish();
                } else {
                    tvStatus.setText("Failed to launch website. Try again.");
                    tvStatus.setVisibility(TextView.VISIBLE);
                }
            }
        });
    }

    private void loadWebsitePreview(String domain, String websiteType) {
        String siteTitle = getIntent().getStringExtra("site_title");
        String description = getIntent().getStringExtra("description");
        String heroImage = getIntent().getStringExtra("hero_image");

        if (siteTitle != null && description != null) {
            String htmlContent = "<html><body style='font-family:sans-serif;padding:16px'>" +
                    "<h1>" + escape(siteTitle) + "</h1>" +
                    (heroImage != null ? "<img src='" + heroImage + "' style='max-width:100%;height:auto'/>" : "") +
                    "<p>" + escape(description) + "</p></body></html>";
            webViewPreview.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null);
            return;
        }

        String type = websiteType == null ? "" : websiteType.toLowerCase();
        String previewUrl;
        switch (type) {
            case "online store":
                previewUrl = "https://example.com/store-template";
                break;
            case "blog":
                previewUrl = "https://example.com/blog-template";
                break;
            case "portfolio":
                previewUrl = "https://example.com/portfolio-template";
                break;
            default:
                previewUrl = "https://example.com/profile-template";
                break;
        }

        try {
            webViewPreview.loadUrl(previewUrl);
        } catch (Exception e) {
            String html = generateDummyHtml(websiteType == null ? "Website" : websiteType, domain);
            webViewPreview.loadDataWithBaseURL(null, html, "text/html", "UTF-8", null);
        }
    }

    private boolean launchWebsite(String domain) {
        // TODO: real API call
        return true;
    }

    private String generateDummyHtml(String websiteType, String domain) {
        return "<html><body style='font-family:sans-serif;background:#04143C;color:#FFFFFF;padding:24px'>" +
                "<h2>Instant Preview</h2>" +
                "<p>Launching demo site for <b>" + escape(domain) + "</b></p>" +
                "<p>This is a preview of your " + escape(websiteType) + " website.</p>" +
                "</body></html>";
    }

    private String escape(String s) {
        if (s == null)
            return "";
        return s.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;");
    }

    private void publishWebsite() {
        if (domain == null || htmlContent == null) {
            tvStatus.setText("Missing domain or HTML content");
            tvStatus.setTextColor(ContextCompat.getColor(this, android.R.color.holo_red_dark));
            tvStatus.setVisibility(TextView.VISIBLE);
            return;
        }

        tvStatus.setText("Publishing website...");
        tvStatus.setTextColor(ContextCompat.getColor(this, android.R.color.holo_blue_dark));
        tvStatus.setVisibility(TextView.VISIBLE);
        btnLaunchSite.setEnabled(false);

        new PublishWebsiteTask().execute();
    }

    private class PublishWebsiteTask extends AsyncTask<Void, Void, Map<String, String>> {

        @Override
        protected Map<String, String> doInBackground(Void... voids) {
            try {
                JsonObject user = new JsonObject();
                user.addProperty("name", userName != null ? userName : (businessName != null ? businessName : "User"));
                user.addProperty("email", userEmail != null ? userEmail
                        : (mAuth.getCurrentUser() != null ? mAuth.getCurrentUser().getEmail() : "<EMAIL>"));
                user.addProperty("password", userPassword != null ? userPassword : "generated-password-123");

                JsonObject payload = new JsonObject();
                payload.add("user", user);
                String subdomain = domain != null ? domain.replace(".co.ke", "").replace(".ke", "") : "";
                payload.addProperty("subdomain", subdomain);
                payload.addProperty("html_content", htmlContent);

                Log.d(TAG, "PublishWebsiteTask payload: " + gson.toJson(payload));

                RequestBody body = RequestBody.create(gson.toJson(payload), JSON_MEDIA_TYPE);
                Request.Builder requestBuilder = new Request.Builder()
                        .url(API_BASE + "create-site.php")
                        .post(body);

                // Add Firebase ID token if authenticated
                if (mAuth.getCurrentUser() != null) {
                    String idToken = mAuth.getCurrentUser().getIdToken(false).getResult().getToken();
                    if (idToken != null) {
                        requestBuilder.addHeader("Authorization", "Bearer " + idToken);
                    }
                }

                Response response = client.newCall(requestBuilder.build()).execute();
                String json = response.body().string();
                Log.d(TAG, "PublishWebsiteTask response: HTTP " + response.code() + ", Body: " + json);

                if (!response.isSuccessful()) {
                    Map<String, String> result = new HashMap<>();
                    result.put("status", "error");
                    result.put("message", "HTTP " + response.code() + ": Failed to publish website");
                    return result;
                }

                JsonObject resJson = gson.fromJson(json, JsonObject.class);
                Map<String, String> result = new HashMap<>();
                result.put("status", resJson.get("status").getAsString());
                if (resJson.has("live_url")) {
                    result.put("live_url", resJson.get("live_url").getAsString());
                    result.put("message", resJson.get("message").getAsString());
                } else if (resJson.has("message")) {
                    result.put("message", resJson.get("message").getAsString());
                }
                return result;
            } catch (IOException e) {
                Log.e(TAG, "PublishWebsiteTask error: " + e.getMessage());
                Map<String, String> result = new HashMap<>();
                result.put("status", "error");
                result.put("message", "Network error: " + e.getMessage());
                return result;
            } catch (Exception e) {
                Log.e(TAG, "PublishWebsiteTask unexpected error: " + e.getMessage());
                Map<String, String> result = new HashMap<>();
                result.put("status", "error");
                result.put("message", "Unexpected error: " + e.getMessage());
                return result;
            }
        }

        @Override
        protected void onPostExecute(Map<String, String> result) {
            btnLaunchSite.setEnabled(true);

            if (result == null || result.get("status").equals("error")) {
                String message = result != null && result.containsKey("message") ? result.get("message")
                        : "Failed to publish website";
                tvStatus.setText(message);
                tvStatus.setTextColor(ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_red_dark));
                tvStatus.setVisibility(TextView.VISIBLE);
                return;
            }

            String liveUrl = result.get("live_url");

            // Save to Firebase if user is authenticated
            if (mAuth.getCurrentUser() != null) {
                String userId = mAuth.getCurrentUser().getUid();
                DatabaseReference siteRef = db.child("sites").child(domain.replace(".", "_"));
                Map<String, Object> data = new HashMap<>();
                data.put("uId", userId);
                data.put("domain", domain);
                data.put("liveUrl", liveUrl);
                data.put("websiteType", websiteType);
                data.put("businessName", businessName != null ? businessName : "");
                data.put("logoUrl", logoDataUrl != null ? logoDataUrl : "");
                data.put("createdAt", System.currentTimeMillis());

                siteRef.setValue(data)
                        .addOnSuccessListener(aVoid -> {
                            tvStatus.setText("Website published successfully! Visit: " + liveUrl);
                            tvStatus.setTextColor(
                                    ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_green_dark));
                        })
                        .addOnFailureListener(e -> {
                            tvStatus.setText("Website published but failed to save to database: " + e.getMessage());
                            tvStatus.setTextColor(
                                    ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_orange_dark));
                        });
            } else {
                tvStatus.setText("Website published successfully! Visit: " + liveUrl);
                tvStatus.setTextColor(ContextCompat.getColor(PreviewActivity.this, android.R.color.holo_green_dark));
            }

            tvStatus.setVisibility(TextView.VISIBLE);

            // Navigate to MainActivity/Dashboard
            Intent intent = new Intent(PreviewActivity.this, com.capson.oneclick.activities.MainActivity.class);
            intent.putExtra("domain", domain);
            intent.putExtra("website_type", websiteType);
            intent.putExtra("live_url", liveUrl);
            if (logoDataUrl != null) {
                intent.putExtra("logo_data_url", logoDataUrl);
            }
            startActivity(intent);
            finish();
        }
    }
}
