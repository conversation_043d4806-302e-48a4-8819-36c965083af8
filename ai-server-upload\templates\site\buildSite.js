export function composeHtml({ theme, pages }){
  const head = `
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      :root{ --brand:${theme.brandColor}; --r:${theme.radius}px; --s:${theme.spacing}px }
      .btn{ border-radius:var(--r); padding:0.75rem 1rem; background:var(--brand); color:white }
      .card{ border-radius:var(--r); padding:var(--s); background:#111827; }
      body{ font-family:${theme.font}, system-ui, -apple-system, Segoe UI, Roboto }
      a{ color: var(--brand) }
    </style>
  </head>`;
  const wrap = (body) => `<!doctype html><html>${head}<body class="bg-zinc-900 text-zinc-100">${body}</body></html>`;

  const home = pages.home || `
    <main class="max-w-5xl mx-auto p-6">
      <h1 class="text-3xl font-bold">Welcome</h1>
      <p class="opacity-80 mt-2">Your starter website is ready.</p>
      <div class="mt-6"><a class="btn" href="products.html">Browse Products</a></div>
    </main>`;

  const about = pages.about || `
    <main class="max-w-4xl mx-auto p-6">
      <h1 class="text-2xl font-bold">About Us</h1>
      <p class="opacity-80 mt-2">We solve local problems with simple, fast experiences.</p>
    </main>`;

  const contact = pages.contact || `
    <main class="max-w-4xl mx-auto p-6">
      <h1 class="text-2xl font-bold">Contact</h1>
      <p class="opacity-80 mt-2">Email: <EMAIL></p>
    </main>`;

  const products = pages.products || `
    <main class="max-w-6xl mx-auto p-6">
      <h1 class="text-2xl font-bold">Products</h1>
      <section class="grid grid-cols-2 md:grid-cols-3 gap-6 mt-6">
        ${Array.from({length:6}).map((_,i)=>`
          <a class="card block hover:opacity-90" href="product-${i+1}.html">
            <div class="h-40 bg-zinc-800 rounded-2xl mb-3"></div>
            <h3 class="text-lg font-semibold">Product ${i+1}</h3>
            <p class="text-sm opacity-80">KSh ${(i+1)*750}</p>
            <button class="btn mt-3">Add to cart</button>
          </a>
        `).join("")}
      </section>
    </main>`;

  const files = {
    "index.html": wrap(home),
    "about.html": wrap(about),
    "contact.html": wrap(contact),
    "products.html": wrap(products),
    "product-1.html": wrap(`
      <main class="max-w-4xl mx-auto p-6">
        <div class="grid md:grid-cols-2 gap-8">
          <div class="h-72 bg-zinc-800 rounded-2xl"></div>
          <div>
            <h1 class="text-2xl font-bold mb-2">Product 1</h1>
            <p class="mb-4">Short description. Great value.</p>
            <div class="text-xl font-semibold mb-6">KSh 750</div>
            <button class="btn">Add to cart</button>
          </div>
        </div>
      </main>
    `)
  };
  return files;
}
