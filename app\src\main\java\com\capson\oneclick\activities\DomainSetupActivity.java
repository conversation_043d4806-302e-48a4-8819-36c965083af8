package com.capson.oneclick.activities;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.os.Bundle;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.capson.oneclick.R;
import com.capson.oneclick.TestConfig;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class DomainSetupActivity extends AppCompatActivity {

    private static final String API_BASE = "https://oneclick.me.ke/api/";
    private static final String CORE_BASE = "https://oneclick.me.ke/ai"; // TODO: Replace with actual domain
                                                                          // suggestion/logo API
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final String TAG = "DomainSetupActivity";

    private EditText etBusinessName, etLogoPrompt;
    private Button btnSuggestDomains, btnPayMpesa, btnGeneratePreview, btnGenerateLogo;
    private TextView tvStatus, labelSuggestions;
    private ImageView ivLogoPreview;
    private ListView lvDomainSuggestions;
    private ArrayAdapter<String> domainAdapter;
    private ArrayList<String> suggestedDomains;
    private Map<String, String> domainAvailability = new HashMap<>();
    private Map<String, String> domainPrices = new HashMap<>();
    private String selectedDomain;
    private String logoDataUrl;
    private String userEmail, userName, userPassword;

    private boolean testMode = false;
    private String identity;
    private String websiteType;

    private final OkHttpClient client = new OkHttpClient();
    private final Gson gson = new Gson();
    private FirebaseAuth mAuth;
    private DatabaseReference db;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_domain_setup);

        // Initialize Firebase
        mAuth = FirebaseAuth.getInstance();
        db = FirebaseDatabase.getInstance().getReference();

        // Toolbar setup
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());

        // Initialize UI elements
        etBusinessName = findViewById(R.id.et_business_name);
        etLogoPrompt = findViewById(R.id.et_logo_prompt);
        btnSuggestDomains = findViewById(R.id.btn_suggest_domains);
        btnPayMpesa = findViewById(R.id.btn_pay_mpesa);
        btnGeneratePreview = findViewById(R.id.btn_deploy_site); // Reusing the same button ID
        btnGenerateLogo = findViewById(R.id.btn_generate_logo);
        tvStatus = findViewById(R.id.tv_status);
        labelSuggestions = findViewById(R.id.label_suggestions);
        ivLogoPreview = findViewById(R.id.iv_logo_preview);
        lvDomainSuggestions = findViewById(R.id.lv_domain_suggestions);

        // Get data from LoginActivity
        Intent intent = getIntent();
        userEmail = intent.getStringExtra("email");
        userName = intent.getStringExtra("name");
        userPassword = intent.getStringExtra("password"); // New: Get password
        identity = intent.getStringExtra("identity");
        websiteType = intent.getStringExtra("website_type");
        testMode = intent.getBooleanExtra("testMode", false);

        // Log user data for debugging
        Log.d(TAG, "User: " + (mAuth.getCurrentUser() != null ? mAuth.getCurrentUser().getUid() : "null") +
                ", Email: " + userEmail + ", Name: " + userName + ", Password: "
                + (userPassword != null ? "set" : "null"));

        // Initialize ListView for domain suggestions
        suggestedDomains = new ArrayList<>();
        domainAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_single_choice, suggestedDomains);
        lvDomainSuggestions.setAdapter(domainAdapter);
        lvDomainSuggestions.setChoiceMode(ListView.CHOICE_MODE_SINGLE);

        // TEST MODE: auto-fill business, auto-suggest, auto-select, enable Pay
        if (testMode) {
            etBusinessName.setText(TestConfig.TEST_BUSINESS_NAME);
            etLogoPrompt.setText("Logo for " + TestConfig.TEST_BUSINESS_NAME);
            new SuggestDomainsTask(TestConfig.TEST_BUSINESS_NAME).execute();
            labelSuggestions.setVisibility(View.VISIBLE);
            lvDomainSuggestions.setVisibility(View.VISIBLE);

            selectedDomain = TestConfig.TEST_DOMAIN;
            int selectedIndex = ensureSuggestedAndGetIndex(selectedDomain);
            lvDomainSuggestions.setItemChecked(selectedIndex, true);

            tvStatus.setText(selectedDomain + " is available! Price: 1200.00 KES");
            tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            tvStatus.setVisibility(View.VISIBLE);

            btnPayMpesa.setEnabled(true);
            btnPayMpesa.setVisibility(View.VISIBLE);
            btnGeneratePreview.setVisibility(View.VISIBLE);
            btnGeneratePreview.setText("Generate & Preview Site");

            new GenerateLogoTask("Logo for " + TestConfig.TEST_BUSINESS_NAME).execute();
        } else {
            btnPayMpesa.setEnabled(false);
            btnGeneratePreview.setVisibility(View.GONE);
            ivLogoPreview.setVisibility(View.GONE);
        }

        btnSuggestDomains.setOnClickListener(v -> {
            String businessName = etBusinessName.getText().toString().trim();
            if (businessName.isEmpty()) {
                tvStatus.setText("Please enter a business name");
                tvStatus.setVisibility(View.VISIBLE);
                return;
            }
            new SuggestDomainsTask(businessName).execute();
        });

        btnGenerateLogo.setOnClickListener(v -> {
            String prompt = etLogoPrompt.getText().toString().trim();
            if (prompt.isEmpty()) {
                prompt = selectedDomain != null ? "Logo for " + selectedDomain : "Default business logo";
            }
            new GenerateLogoTask(prompt).execute();
        });

        lvDomainSuggestions.setOnItemClickListener((parent, view, position, id) -> {
            selectedDomain = suggestedDomains.get(position);
            new CheckAvailabilityTask(selectedDomain, false).execute();
        });

        btnPayMpesa.setOnClickListener(v -> {
            if (selectedDomain == null) {
                tvStatus.setText("Please select a domain");
                tvStatus.setVisibility(View.VISIBLE);
                return;
            }
            if (processMpesaPayment(selectedDomain)) {
                tvStatus.setText("Payment successful! Ready to generate preview.");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                btnGeneratePreview.setVisibility(View.VISIBLE);
                btnPayMpesa.setVisibility(View.GONE);

                // Save domain to Firebase
                if (mAuth.getCurrentUser() != null) {
                    String userId = mAuth.getCurrentUser().getUid();
                    DatabaseReference domainRef = db.child("domains").child(selectedDomain.replace(".", "_"));
                    Map<String, Object> data = new HashMap<>();
                    data.put("uId", userId);
                    data.put("domain", selectedDomain);
                    data.put("price", domainPrices.getOrDefault(selectedDomain, "0.00 KES"));
                    data.put("businessName", etBusinessName.getText().toString().trim());
                    data.put("createdAt", System.currentTimeMillis());
                    domainRef.setValue(data)
                            .addOnSuccessListener(aVoid -> {
                                tvStatus.setText("Domain saved to database!");
                            })
                            .addOnFailureListener(e -> {
                                tvStatus.setText("Failed to save domain: " + e.getMessage());
                                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                            });
                } else {
                    tvStatus.setText("Payment successful but domain not saved: User not authenticated");
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
                }
            } else {
                tvStatus.setText("Payment failed. Please try again.");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            }
            tvStatus.setVisibility(View.VISIBLE);
        });

        btnGeneratePreview.setOnClickListener(v -> {
            if (selectedDomain == null) {
                tvStatus.setText("Please select a domain before generating preview");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                tvStatus.setVisibility(View.VISIBLE);
                return;
            }
            if (mAuth.getCurrentUser() == null && userEmail == null) {
                tvStatus.setText("Please log in to generate preview");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                tvStatus.setVisibility(View.VISIBLE);
                Intent intentLogin = new Intent(DomainSetupActivity.this,
                        com.capson.oneclick.activities.LoginActivity.class);
                startActivity(intentLogin);
                return;
            }
            new GeneratePreviewTask().execute();
        });
    }

    private class SuggestDomainsTask extends AsyncTask<Void, Void, List<String>> {
        private final String businessName;

        SuggestDomainsTask(String businessName) {
            this.businessName = businessName;
        }

        @Override
        protected List<String> doInBackground(Void... voids) {
            try {
                JsonObject payload = new JsonObject();
                payload.addProperty("idea", businessName);
                payload.addProperty("bilingual", true);
                payload.addProperty("locale", "en-KE");
                if (identity != null)
                    payload.addProperty("audience", identity);

                RequestBody body = RequestBody.create(gson.toJson(payload), JSON_MEDIA_TYPE);
                Request request = new Request.Builder()
                        .url(CORE_BASE + "/v1/domains/suggest")
                        .post(body)
                        .build();

                Response response = client.newCall(request).execute();
                if (!response.isSuccessful()) {
                    Log.e(TAG, "SuggestDomainsTask failed: HTTP " + response.code());
                    return null;
                }

                String json = response.body().string();
                Log.d(TAG, "SuggestDomainsTask response: " + json);
                JsonObject resJson = gson.fromJson(json, JsonObject.class);
                Type listType = new TypeToken<List<String>>() {
                }.getType();
                return gson.fromJson(resJson.get("domainIdeas"), listType);
            } catch (IOException e) {
                Log.e(TAG, "SuggestDomainsTask error: " + e.getMessage());
                return null;
            }
        }

        @Override
        protected void onPostExecute(List<String> result) {
            if (result == null) {
                tvStatus.setText("Failed to suggest domains");
                tvStatus.setVisibility(View.VISIBLE);
                return;
            }

            suggestedDomains.clear();
            suggestedDomains.addAll(result);
            domainAdapter.notifyDataSetChanged();
            labelSuggestions.setVisibility(View.VISIBLE);
            lvDomainSuggestions.setVisibility(View.VISIBLE);
            btnPayMpesa.setVisibility(View.VISIBLE);
            tvStatus.setVisibility(View.GONE);

            for (String d : suggestedDomains) {
                new CheckAvailabilityTask(d, true).execute();
            }
        }
    }

    private class CheckAvailabilityTask extends AsyncTask<Void, Void, Map<String, Object>> {
        private final String domainToCheck;
        private final boolean isBackground;

        CheckAvailabilityTask(String domain, boolean isBackground) {
            this.domainToCheck = domain;
            this.isBackground = isBackground;
        }

        @Override
        protected Map<String, Object> doInBackground(Void... voids) {
            try {
                String url = API_BASE + "check-domain.php?domain=" + domainToCheck;
                Request request = new Request.Builder().url(url).build();
                Response response = client.newCall(request).execute();
                if (!response.isSuccessful()) {
                    Log.e(TAG, "CheckAvailabilityTask failed: HTTP " + response.code());
                    return null;
                }

                String json = response.body().string();
                Log.d(TAG, "CheckAvailabilityTask response for " + domainToCheck + ": " + json);
                JsonObject resJson = gson.fromJson(json, JsonObject.class);
                Map<String, Object> result = new HashMap<>();
                result.put("status", resJson.get("status").getAsString());
                if (resJson.has("available")) {
                    result.put("available", resJson.get("available").getAsBoolean());
                    result.put("price", resJson.get("price").getAsString());
                } else if (resJson.has("message")) {
                    result.put("message", resJson.get("message").getAsString());
                }
                return result;
            } catch (IOException e) {
                Log.e(TAG, "CheckAvailabilityTask error: " + e.getMessage());
                return null;
            }
        }

        @Override
        protected void onPostExecute(Map<String, Object> result) {
            if (result == null || result.get("status").equals("error")) {
                String message = result != null && result.containsKey("message") ? (String) result.get("message")
                        : "Failed to check domain";
                domainAvailability.put(domainToCheck, "unknown");
                if (!isBackground) {
                    tvStatus.setText(message);
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                    tvStatus.setVisibility(View.VISIBLE);
                    btnPayMpesa.setEnabled(false);
                }
                return;
            }

            boolean available = (Boolean) result.get("available");
            String price = (String) result.get("price");
            domainAvailability.put(domainToCheck, available ? "available" : "taken");
            domainPrices.put(domainToCheck, price);
            domainAdapter.notifyDataSetChanged();

            if (!isBackground) {
                if (available) {
                    tvStatus.setText(domainToCheck + " is available! Price: " + price);
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                    btnPayMpesa.setEnabled(true);
                } else {
                    tvStatus.setText(domainToCheck + " is not available. Choose another.");
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                    btnPayMpesa.setEnabled(false);
                }
                tvStatus.setVisibility(View.VISIBLE);
            }
        }
    }

    private class GenerateLogoTask extends AsyncTask<Void, Void, String> {
        private final String prompt;

        GenerateLogoTask(String prompt) {
            this.prompt = prompt;
        }

        @Override
        protected String doInBackground(Void... voids) {
            try {
                JsonObject payload = new JsonObject();
                payload.addProperty("prompt",
                        prompt + ". Clean vector-style logomark on neutral background, modern and simple.");

                RequestBody body = RequestBody.create(gson.toJson(payload), JSON_MEDIA_TYPE);
                Request request = new Request.Builder()
                        .url(CORE_BASE + "/v1/logo/generate")
                        .post(body)
                        .build();

                Response response = client.newCall(request).execute();
                if (!response.isSuccessful()) {
                    Log.e(TAG, "GenerateLogoTask failed: HTTP " + response.code());
                    return null;
                }

                String json = response.body().string();
                Log.d(TAG, "GenerateLogoTask response: " + json);
                JsonObject resJson = gson.fromJson(json, JsonObject.class);
                return resJson.get("dataUrl").getAsString();
            } catch (IOException e) {
                Log.e(TAG, "GenerateLogoTask error: " + e.getMessage());
                return null;
            }
        }

        @Override
        protected void onPostExecute(String dataUrl) {
    if (dataUrl != null && !dataUrl.isEmpty()) {
        logoDataUrl = dataUrl;
        ivLogoPreview.setVisibility(View.VISIBLE);

        try {
            if (dataUrl.startsWith("data:image")) {
                // --- It's a Base64 string, decode it ---
                String base64 = dataUrl.substring(dataUrl.indexOf(",") + 1);
                byte[] decoded = Base64.decode(base64, Base64.DEFAULT);
                Bitmap bitmap = BitmapFactory.decodeByteArray(decoded, 0, decoded.length);
                ivLogoPreview.setImageBitmap(bitmap);
                tvStatus.setText("Logo generated successfully!");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));

            } else if (dataUrl.startsWith("http")) {
                // --- It's a web URL, we need to load it from the network ---
                // This requires an image loading library like Picasso or Glide.
                // For a quick fix without adding a new library, we can do it in another AsyncTask.
                tvStatus.setText("Loading logo from URL...");
                new DownloadImageTask(ivLogoPreview).execute(dataUrl);

            } else {
                throw new IllegalArgumentException("Unknown dataUrl format");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to display logo", e);
            tvStatus.setText("Failed to display logo");
            tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    } else {
        tvStatus.setText("Logo generation failed");
        tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        ivLogoPreview.setVisibility(View.GONE);
    }
    tvStatus.setVisibility(View.VISIBLE);
}

// You also need to add this new helper class inside DomainSetupActivity.java
// This private class downloads an image from a URL in the background.
private class DownloadImageTask extends AsyncTask<String, Void, Bitmap> {
    ImageView bmImage;

    public DownloadImageTask(ImageView bmImage) {
        this.bmImage = bmImage;
    }

    protected Bitmap doInBackground(String... urls) {
        String urldisplay = urls[0];
        Bitmap bmp = null;
        try {
            java.io.InputStream in = new java.net.URL(urldisplay).openStream();
            bmp = BitmapFactory.decodeStream(in);
        } catch (Exception e) {
            Log.e("Error", e.getMessage());
            e.printStackTrace();
        }
        return bmp;
    }

    protected void onPostExecute(Bitmap result) {
        if (result != null) {
            bmImage.setImageBitmap(result);
        }
    }
}

    private class GeneratePreviewTask extends AsyncTask<Void, Void, Map<String, String>> {

        @Override
        protected Map<String, String> doInBackground(Void... voids) {
            try {
                JsonObject payload = new JsonObject();
                payload.addProperty("businessName", etBusinessName.getText().toString().trim());
                payload.addProperty("domain", selectedDomain);
                payload.addProperty("websiteType", websiteType != null ? websiteType : "Portfolio");
                payload.addProperty("identity", identity != null ? identity : "");
                if (logoDataUrl != null) {
                    payload.addProperty("logoDataUrl", logoDataUrl);
                }

                Log.d(TAG, "GeneratePreviewTask payload: " + gson.toJson(payload));

                RequestBody body = RequestBody.create(gson.toJson(payload), JSON_MEDIA_TYPE);
                Request request = new Request.Builder()
                        .url(CORE_BASE + "/v1/sites/generate")
                        .post(body)
                        .build();

                Response response = client.newCall(request).execute();
                String json = response.body().string();
                Log.d(TAG, "GeneratePreviewTask response: HTTP " + response.code() + ", Body: " + json);

                if (json == null || json.trim().isEmpty()) {
                    Map<String, String> result = new HashMap<>();
                    result.put("status", "error");
                    result.put("message", "Empty response from AI service");
                    return result;
                }

                if (!response.isSuccessful()) {
                    Map<String, String> result = new HashMap<>();
                    result.put("status", "error");
                    result.put("message", "HTTP " + response.code() + ": Failed to generate site preview");
                    return result;
                }

                JsonObject resJson;
                try {
                    resJson = gson.fromJson(json, JsonObject.class);
                } catch (Exception e) {
                    Log.e(TAG, "Failed to parse JSON response: " + e.getMessage());
                    Map<String, String> result = new HashMap<>();
                    result.put("status", "error");
                    result.put("message", "Invalid JSON response from AI service");
                    return result;
                }

                Map<String, String> result = new HashMap<>();

                // Check if the response has the expected 'files' structure
                if (resJson != null && resJson.has("files") && resJson.get("files").isJsonObject()) {
                    JsonObject filesObject = resJson.getAsJsonObject("files");
                    if (filesObject.has("index.html")) {
                        String theHtmlString = filesObject.get("index.html").getAsString();
                        result.put("status", "success");
                        result.put("html_content", theHtmlString);
                        result.put("message", "Site preview generated successfully");
                        Log.d(TAG, "Successfully extracted HTML content, length: " + theHtmlString.length());
                    } else {
                        result.put("status", "error");
                        result.put("message", "No index.html found in files object");
                        Log.e(TAG, "Files object keys: " + filesObject.keySet().toString());
                    }
                } else {
                    result.put("status", "error");
                    result.put("message", "Invalid response format from AI service - missing 'files' object");
                    Log.e(TAG, "Response JSON keys: " + (resJson != null ? resJson.keySet().toString() : "null"));
                }

                return result;
            } catch (IOException e) {
                Log.e(TAG, "GeneratePreviewTask network error: " + e.getMessage());

                // Fallback: Generate a simple HTML template for testing
                if (testMode) {
                    Log.d(TAG, "Test mode: generating fallback HTML");
                    String fallbackHtml = generateFallbackHtml();
                    Map<String, String> result = new HashMap<>();
                    result.put("status", "success");
                    result.put("html_content", fallbackHtml);
                    result.put("message", "Using fallback HTML (test mode)");
                    return result;
                }

                Map<String, String> result = new HashMap<>();
                result.put("status", "error");
                result.put("message", "Network error: " + e.getMessage());
                return result;
            } catch (Exception e) {
                Log.e(TAG, "GeneratePreviewTask unexpected error: " + e.getMessage());
                Map<String, String> result = new HashMap<>();
                result.put("status", "error");
                result.put("message", "Unexpected error: " + e.getMessage());
                return result;
            }
        }

        @Override
        protected void onPostExecute(Map<String, String> result) {
            if (result == null || result.get("status").equals("error")) {
                String message = result != null && result.containsKey("message") ? result.get("message")
                        : "Failed to generate site preview";
                tvStatus.setText(message);
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                tvStatus.setVisibility(View.VISIBLE);
                return;
            }

            String htmlContent = result.get("html_content");
            if (htmlContent != null && !htmlContent.isEmpty()) {
                tvStatus.setText("Preview generated successfully! Opening preview...");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                tvStatus.setVisibility(View.VISIBLE);

                // Launch PreviewActivity with the generated HTML content
                Intent intent = new Intent(DomainSetupActivity.this, PreviewActivity.class);
                intent.putExtra("domain", selectedDomain);
                intent.putExtra("website_type", websiteType);
                intent.putExtra("html_content", htmlContent);
                intent.putExtra("business_name", etBusinessName.getText().toString().trim());
                intent.putExtra("user_email", userEmail);
                intent.putExtra("user_name", userName);
                intent.putExtra("user_password", userPassword);
                if (logoDataUrl != null) {
                    intent.putExtra("logo_data_url", logoDataUrl);
                }
                startActivity(intent);
            } else {
                tvStatus.setText("Failed to generate HTML content");
                tvStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
                tvStatus.setVisibility(View.VISIBLE);
            }
        }
    }

    private int ensureSuggestedAndGetIndex(String domain) {
        int idx = suggestedDomains.indexOf(domain);
        if (idx < 0) {
            suggestedDomains.add(0, domain);
            domainAdapter.notifyDataSetChanged();
            idx = 0;
        }
        return idx;
    }

    private boolean processMpesaPayment(String domain) {
        // TODO: Integrate M-PESA Daraja API
        Log.d(TAG, "Simulating M-PESA payment for domain: " + domain);
        return true; // Simulate success
    }

    private String generateFallbackHtml() {
        String businessName = etBusinessName.getText().toString().trim();
        if (businessName.isEmpty())
            businessName = "Your Business";

        return "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>" + businessName + " - " + selectedDomain + "</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n"
                +
                "        .container { max-width: 800px; margin: 0 auto; text-align: center; }\n" +
                "        h1 { font-size: 3em; margin-bottom: 0.5em; }\n" +
                "        .subtitle { font-size: 1.2em; margin-bottom: 2em; opacity: 0.9; }\n" +
                "        .logo { width: 150px; height: 150px; margin: 20px auto; border-radius: 50%; background: rgba(255,255,255,0.1); display: flex; align-items: center; justify-content: center; font-size: 2em; }\n"
                +
                "        .section { background: rgba(255,255,255,0.1); padding: 30px; margin: 20px 0; border-radius: 10px; }\n"
                +
                "        .cta { background: #ff6b6b; color: white; padding: 15px 30px; border: none; border-radius: 25px; font-size: 1.1em; cursor: pointer; margin: 10px; }\n"
                +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                (logoDataUrl != null ? "        <img src=\"" + logoDataUrl
                        + "\" alt=\"Logo\" style=\"width: 150px; height: 150px; border-radius: 50%; margin: 20px auto; display: block;\">\n"
                        : "        <div class=\"logo\">📱</div>\n")
                +
                "        <h1>Welcome to " + businessName + "</h1>\n" +
                "        <p class=\"subtitle\">Your digital presence starts here</p>\n" +
                "        \n" +
                "        <div class=\"section\">\n" +
                "            <h2>About Us</h2>\n" +
                "            <p>We are " + businessName
                + ", committed to providing excellent service and quality products to our customers in Kenya and beyond.</p>\n"
                +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"section\">\n" +
                "            <h2>Our Services</h2>\n" +
                "            <p>Discover what makes us special. We offer innovative solutions tailored to your needs.</p>\n"
                +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"section\">\n" +
                "            <h2>Get In Touch</h2>\n" +
                "            <p>Ready to work with us? Contact us today!</p>\n" +
                "            <button class=\"cta\">Contact Us</button>\n" +
                "            <button class=\"cta\">Learn More</button>\n" +
                "        </div>\n" +
                "        \n" +
                "        <p style=\"margin-top: 40px; opacity: 0.7;\">Powered by OneClick Kenya</p>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }
}