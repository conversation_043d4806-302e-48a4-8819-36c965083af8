<resources>
    <style name="Base.Theme.OneClick" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/ColorSecondary</item>
        <item name="colorPrimaryVariant">@color/ColorSecondaryDark</item>
        <item name="colorOnPrimary">@color/white</item>

        <item name="colorSecondary">@color/blueAccent</item>
        <item name="colorOnSecondary">@color/white</item>

<!--        <item name="ColorBackground">@color/ColorBackground</item>-->
        <item name="colorSurface">@color/ColorPrimaryDark</item>
        <item name="colorOnSurface">@color/white</item>

        <item name="materialButtonStyle">@style/Widget.OneClick.Button.Filled</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.OneClick.Button.Outlined</item>

        <item name="colorAccent">@color/ColorSecondary</item>
    </style>
</resources>
