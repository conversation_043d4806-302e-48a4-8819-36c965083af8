<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:background="@drawable/bg_split_curved"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ColorPrimary">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/ColorPrimaryDark"
            app:title="Dashboard"
            android:paddingStart="8dp"
            app:titleTextColor="@android:color/white"
            app:menu="@menu/toolbar_menu"
            app:layout_constraintTop_toTopOf="parent"
            app:navigationIcon="@drawable/ic_back_arrow"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <!-- Fragment Container (This is where the dashboard/profile loads) -->
        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="0dp"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            app:layout_constraintBottom_toTopOf="@id/bottom_navigation"/>

        <!-- Bottom Navigation -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/nav_background2"
            android:backgroundTint="@color/ColorPrimary"
            app:itemBackground="@drawable/nav_item_background2"
            android:elevation="8dp"
            app:itemIconTint="@color/navIconUnselected"
            app:itemTextColor="@color/navTextUnselected"
            app:labelVisibilityMode="labeled"
            app:itemPadding="8dp"
            app:itemHorizontalTranslationEnabled="false"
            app:menu="@menu/bottom_nav_menu"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:ignore="VisualLintBottomNav" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <!--    &lt;!&ndash; Navigation Drawer &ndash;&gt;-->
    <!--    <com.google.android.material.navigation.NavigationView-->
    <!--        android:id="@+id/navigation_view"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="match_parent"-->
    <!--        android:layout_gravity="start"-->
    <!--        app:headerLayout="@layout/nav_header"-->
    <!--        app:menu="@menu/nav_menu"-->
    <!--        app:itemIconTint="@color/navIconUnselected"-->
    <!--        app:itemTextColor="@color/navTextUnselected"-->
    <!--        android:background="@drawable/nav_background"-->
    <!--        app:itemBackground="@drawable/nav_item_background"-->
    <!--        tools:ignore="VisualLintBounds" />-->


</androidx.drawerlayout.widget.DrawerLayout>
