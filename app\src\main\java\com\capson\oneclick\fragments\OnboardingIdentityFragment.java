package com.capson.oneclick.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.capson.oneclick.R;
import com.google.android.material.card.MaterialCardView;

public class OnboardingIdentityFragment extends Fragment {

    public interface Listener {
        void onIdentitySelected(String identity);
    }

    private Listener listener;
    private MaterialCardView selected;

    public OnboardingIdentityFragment(Listener listener) {
        this.listener = listener;
    }

    public OnboardingIdentityFragment() { } // required empty constructor

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_onboarding_identity, container, false);

        MaterialCardView cardSme = v.findViewById(R.id.card_sme);
        MaterialCardView cardCreator = v.findViewById(R.id.card_creator);
        MaterialCardView cardStudent = v.findViewById(R.id.card_student);
        MaterialCardView cardOther = v.findViewById(R.id.card_other);

        setup(cardSme, "Small Business (SME)");
        setup(cardCreator, "Creator");
        setup(cardStudent, "Student");
        setup(cardOther, "Other");

        return v;
    }

    private void setup(MaterialCardView card, String label) {
        card.setOnClickListener(view -> {
            select(card);
            if (listener != null) listener.onIdentitySelected(label);
        });
    }

    private void select(MaterialCardView card) {
        if (selected != null) {
            selected.setStrokeColor(getResources().getColor(R.color.whiteMore));
            selected.setStrokeWidth(2);
        }
        selected = card;
        selected.setStrokeColor(getResources().getColor(R.color.ColorSecondary));
        selected.setStrokeWidth(5);
    }
}
