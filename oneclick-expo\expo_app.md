# OneClick Expo App (Supabase) — Product & Implementation Plan

## 1. Goal & Scope

Deliver an Expo app (TypeScript) that lets a user:
- Authenticate (email/password or magic link) via Supabase.
- Describe their business and choose a site type and template.
- Get domain suggestions from the AI server and check availability with the Hosting server.
- Generate a preview site (AI JSON → in‑app WebView).
- Publish the final site by requesting a ZIP from the AI server and uploading it to the Hosting server.
- View their published sites and basic details in a dashboard.

Firebase is not used. We integrate Supabase for auth, database, and storage.

## 2. Tech Stack

- App: Expo (SDK 51+) + Expo Router; TypeScript.
- UI: NativeWind or React Native Paper; React Native WebView for preview.
- Networking: Fetch + TanStack Query (React Query) for caching, retries, and loading states.
- State: Zustand for global app/builder state.
- Auth/DB: Supabase (Auth, Postgres, RLS). Optional Storage for logos/assets.
- Files: expo-file-system, expo-sharing (optional ZIP download for user).
- Analytics (optional): PostHog or Amplitude.
- Config: app.config.ts with expo-constants to read environment extras.

External services in use:
- AI Server (creative engine): https://oneclick.me.ke/ai
  - /v1/domains/suggest, /v1/logo/generate, /v1/sites/generate
- Hosting Server (deployment): https://oneclick.me.ke/api
  - GET check-domain.php, POST create-site.php (multipart with ZIP)

## 3. Environment & Configuration

app.config.ts extras (read via expo-constants):
- EXPO_PUBLIC_AI_BASE (e.g., https://oneclick.me.ke/ai)
- EXPO_PUBLIC_HOSTING_BASE (e.g., https://oneclick.me.ke/api)
- EXPO_PUBLIC_OPENAI_MODEL (optional hint for server /v1/responses)
- EXPO_PUBLIC_SUPABASE_URL
- EXPO_PUBLIC_SUPABASE_ANON_KEY

No secret keys for OpenAI in the client — AI server owns those.

## 4. Supabase Database Schema (MVP)

All tables use RLS with policies that restrict rows to auth.uid().

- profiles
  - id uuid PK, references auth.users.id
  - email text
  - name text
  - created_at timestamptz default now()
  - Policies: user can read/write own row.

- sites
  - id uuid PK default gen_random_uuid()
  - owner_id uuid references auth.users.id
  - domain text (e.g., mybrand.co.ke)
  - subdomain text (derived from domain, e.g., mybrand)
  - template_style text check in ('colorful','cool','minimal','sleek','playful')
  - business_name text
  - logo_url text (data URL or remote URL stored as text; optional Storage path)
  - live_url text (filled after publish)
  - status text default 'draft' (draft | preview_ready | published | failed)
  - created_at timestamptz default now()
  - updated_at timestamptz default now()
  - Policies: owner can CRUD; others no access.

- generation_jobs (optional, for troubleshooting/metrics)
  - id uuid PK
  - site_id uuid references sites.id
  - phase text ('preview'|'publish')
  - request jsonb
  - response_status int
  - error text
  - created_at timestamptz default now()
  - Policies: owner can read their rows.

Indexes:
- sites(owner_id)
- sites(domain) unique for each owner or globally (decide per business rules).

RLS Policies (examples):
- profiles: using (id = auth.uid()) with check (id = auth.uid())
- sites: using (owner_id = auth.uid()) with check (owner_id = auth.uid())

## 5. App Structure (Folders)

oneclick-expo/
  app/
    (auth)/login.tsx
    (auth)/signup.tsx
    (onboarding)/identity.tsx
    (onboarding)/site-type.tsx
    (builder)/domain.tsx
    (builder)/logo.tsx
    (builder)/template.tsx
    (builder)/preview.tsx
    (builder)/publish.tsx
    (dashboard)/index.tsx
    (dashboard)/settings.tsx
    _layout.tsx
    index.tsx
  src/
    api/
      supabase.ts      # initialize client
      ai.ts            # AI server calls
      hosting.ts       # hosting server calls
    state/
      auth.store.ts
      builder.store.ts
    components/
      Button.tsx
      TextField.tsx
      DomainCard.tsx
      AvailabilityBadge.tsx
      LogoPreview.tsx
      TemplatePicker.tsx
      WebPreview.tsx    # wraps WebView
      LoadingOverlay.tsx
      EmptyState.tsx
    hooks/
      useAuth.ts
      useDebounce.ts
    utils/
      types.ts
      validators.ts
      formatting.ts
  app.config.ts
  package.json
  tsconfig.json
  README.md

## 6. State Management

- Zustand slices
  - auth.store
    - user: { id, email, name } | null
    - status: 'idle'|'loading'|'authenticated'
    - actions: signIn, signUp, signOut, setProfile
  - builder.store
    - businessName: string
    - identity: string | null
    - siteType: 'Portfolio'|'Store'|'Blog'|'Profile'
    - selectedDomain: string | null
    - templateStyle: 'colorful'|'cool'|'minimal'|'sleek'|'playful'
    - logoDataUrl: string | null (data: or https URL)
    - files: Record<string,string> (from preview JSON)
    - htmlContent: string (index.html from files)
    - actions: setters + reset

React Query handles network operations (loading, error, retries), Zustand holds wizard state.

## 7. API Layer Contracts

AI Server (EXPO_PUBLIC_AI_BASE):
- POST /v1/domains/suggest body { idea, bilingual?: true, locale?: 'en-KE' } → { domainIdeas: string[] }
- POST /v1/logo/generate body { prompt } → { dataUrl: string } (may be data: or https)
- POST /v1/sites/generate
  - Preview: { domain, businessName, templateStyle?, logoDataUrl?, asZip: false } → { files: {...} }
  - Publish: { domain, businessName, templateStyle?, logoDataUrl?, asZip: true } → ZIP bytes

Hosting Server (EXPO_PUBLIC_HOSTING_BASE):
- GET /check-domain.php?domain=... → { status: 'success', available: boolean, price?: string }
- POST /create-site.php (multipart/form-data)
  - fields: name, email, password, subdomain, website_zip (file)
  - → { status: 'success', live_url, message } | { status: 'error', message }

Supabase:
- Auth: email/password sign-in/up.
- DB: writes to profiles, sites; optional generation_jobs for debugging.

## 8. User Journeys & Stories

1) As a new user, I sign up with email and password so I can access the builder.
2) As a user, I describe my business and choose identity/site type to tailor suggestions.
3) As a user, I request domain suggestions and immediately see which are available and their prices.
4) As a user, I generate a logo from a prompt and preview it.
5) As a user, I select a template style and generate a site preview quickly.
6) As a user, I publish my site; the app handles packaging and deployment and returns a live URL.
7) As a user, I can view my sites on a dashboard with status and click through to live URLs.

Edge cases & flows:
- If suggestions fail, I can retry. If availability API fails for some items, they show Unknown with retry.
- If logo billing is limited (402), app falls back to placeholder and informs me.
- If preview fails, show error and offer regenerate.
- If publish fails (HTTP 500 from Hosting), surface message and prompt to retry.

## 9. Screens & UI (with Loading/Errors)

- Splash (index.tsx)
  - Reads auth.store and routes to (auth) or (dashboard).
  - UI: Fullscreen loader.

- Login (app/(auth)/login.tsx)
  - Fields: email, password; Buttons: Login; Link: Sign up
  - Loading: button spinner during sign-in; disable fields; show auth errors.

- Signup (app/(auth)/signup.tsx)
  - Fields: name, email, password; Button: Sign up
  - On success: create profile row; route to onboarding.
  - Loading: button spinner; error toast on failure.

- Onboarding: Identity (app/(onboarding)/identity.tsx)
  - UI: audience chips (SME, Student, etc.).
  - Next: Site Type.

- Onboarding: Site Type (app/(onboarding)/site-type.tsx)
  - UI: cards (Portfolio, Store, Blog, Profile) with descriptions.
  - Next: Domain.

- Domain (app/(builder)/domain.tsx)
  - UI: text input for “Your business idea or name”, button “Suggest Domains”.
  - On click: call AI /v1/domains/suggest; show skeleton while loading.
  - Results list: each shows domain + AvailabilityBadge.
  - Availability: parallel GET /api/check-domain.php; show loading badge per row, then Available/Taken/Unknown; show price if provided.
  - Select one → persists to builder.store.selectedDomain and a draft sites row in Supabase.

- Logo (app/(builder)/logo.tsx)
  - UI: prompt field prefilled with business name; Generate button.
  - Loading: button spinner; placeholder shimmer for image.
  - Result: show image; if data URL → render; if remote URL → <Image> with uri.

- Template (app/(builder)/template.tsx)
  - UI: 5 cards (colorful/cool/minimal/sleek/playful); selected state.
  - Save to builder.store.

- Preview (app/(builder)/preview.tsx)
  - Button: “Generate Preview”
  - Action: POST /ai/v1/sites/generate { asZip:false }; store files and htmlContent.
  - Loading: overlay “Generating website… Please wait”.
  - WebPreview: render htmlContent in WebView; Button “Publish My Website”.
  - Error: show error panel with retry.

- Publish (app/(builder)/publish.tsx)
  - On open or on button click from Preview: do publish sequence.
  - Step 1: Build ZIP via AI { asZip:true } with spinner “Preparing final package…”.
  - Step 2: Multipart upload to /api/create-site.php with spinner “Deploying to hosting…”.
  - On success: capture live_url, update sites row (status='published'), show success UI with “Open Site” and “Share”.
  - On failure: show error message from server; allow retry.

- Dashboard (app/(dashboard)/index.tsx)
  - List of user’s sites from Supabase with status, domain, live_url.
  - EmptyState when none; button “Create new site”.

- Settings (app/(dashboard)/settings.tsx)
  - Profile details: name, email; Update name; Sign out.

## 10. Security & Policies

- Never include OpenAI keys in the app; only call AI server.
- Use Supabase RLS to restrict rows to the authenticated user.
- Validate subdomain strictly on the client and server; only [a-z0-9-].
- Handle CORS on AI/Hosting as needed (app is mobile; not strictly required, but nice for web previews).

## 11. Development Plan

1) Bootstrap Expo Router TS project; add NativeWind or Paper theme.
2) Configure app.config.ts extras; initialize Supabase client.
3) Create Zustand stores and QueryClient provider in _layout.tsx.
4) Build Auth screens; wire sign-in/up; create profiles row.
5) Implement Onboarding (identity, site type) → set builder state.
6) Domain screen: suggest + availability checks with loading badges.
7) Logo screen: generate + display with loading and error handling.
8) Template screen: select style.
9) Preview screen: call AI { asZip:false }, store files/html, show WebView.
10) Publish screen: call AI { asZip:true } → multipart upload to Hosting → write sites row updates in Supabase.
11) Dashboard: list sites for current user; open live links.
12) QA: test poor connectivity, server 4xx/5xx, and long-running generation with visible progress/feedback.

## 12. Error & Loading Patterns

- Use React Query isLoading/isError for all requests.
- Skeletons/placeholders: domain list items, logo preview box, preview WebView area.
- Disable actions during inflight operations; show toasts/snackbars for errors.
- Timeouts for AI and Hosting calls with a retry button.

## 13. Supabase Policies (examples)

-- profiles
alter table profiles enable row level security;
create policy "own_profile" on profiles
  for all using (id = auth.uid()) with check (id = auth.uid());

-- sites
alter table sites enable row level security;
create policy "own_sites" on sites
  for all using (owner_id = auth.uid()) with check (owner_id = auth.uid());

## 14. Testing Checklist

- Auth: signup/login/logout works; profile row created.
- Domain: suggestions list; availability badges resolve; selection stored.
- Logo: handles data URL and remote URL; error code 402 fallback.
- Preview: builds and renders; error handling present.
- Publish: ZIP generated; multipart upload succeeds; live_url saved; dashboard updates.
- Offline/poor network: graceful messages and retries.

## 15. Nice-to-Haves (Post-MVP)

- Site editor for simple text/image tweaks before publish.
- Store the selected files in Supabase Storage for history.
- Regenerate logo/templates; keep versions per site.
- Push notifications when publish finishes (if publish becomes asynchronous).

---

This plan aligns the Expo app around Supabase for auth/data and your existing AI/Hosting servers for generation and deployment. It includes the DB schema, state model, API contracts, user journeys, and detailed UI with loading/error states so you can implement confidently.
