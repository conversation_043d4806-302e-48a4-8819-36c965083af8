package com.capson.oneclick.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

import com.capson.oneclick.R;
import com.capson.oneclick.TestConfig;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;

import java.util.HashMap;
import java.util.Map;

public class LoginActivity extends AppCompatActivity {

    private EditText etEmail, etPassword, etName;
    private Button btnLogin, btnSignUp;
    private TextView tvError;
    private FirebaseAuth mAuth;
    private DatabaseReference db;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // Initialize Firebase
        mAuth = FirebaseAuth.getInstance();
        db = FirebaseDatabase.getInstance().getReference();

        // Initialize UI elements
        etEmail = findViewById(R.id.et_email);
        etPassword = findViewById(R.id.et_password);
        etName = findViewById(R.id.et_name);
        btnLogin = findViewById(R.id.btn_login);
        btnSignUp = findViewById(R.id.btn_sign_up);
        tvError = findViewById(R.id.tv_error);

        // Test mode: auto-fill credentials
        if (TestConfig.TEST_MODE) {
            etEmail.setText("<EMAIL>");
            etName.setText("Test User");
            etPassword.setText("test1234");
        }

        btnLogin.setOnClickListener(v -> {
            String email = etEmail.getText().toString().trim();
            String password = etPassword.getText().toString().trim();

            if (email.isEmpty() || password.isEmpty()) {
                tvError.setText("Please enter email and password");
                tvError.setVisibility(View.VISIBLE);
                return;
            }

            loginUser(email, password);
        });

        btnSignUp.setOnClickListener(v -> {
            String email = etEmail.getText().toString().trim();
            String password = etPassword.getText().toString().trim();
            String name = etName.getText().toString().trim();

            if (email.isEmpty() || password.isEmpty() || name.isEmpty()) {
                tvError.setText("Please enter name, email, and password");
                tvError.setVisibility(View.VISIBLE);
                return;
            }

            signUpUser(name, email, password);
        });
    }

    private void loginUser(String email, String password) {
        mAuth.signInWithEmailAndPassword(email, password)
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = mAuth.getCurrentUser();
                        if (user != null) {
                            // Fetch user name from Firebase Database
                            db.child("users").child(user.getUid()).child("name").get()
                                    .addOnSuccessListener(dataSnapshot -> {
                                        String name = dataSnapshot.getValue(String.class);
                                        navigateToDomainSetup(email, name != null ? name : "User");
                                    })
                                    .addOnFailureListener(e -> {
                                        tvError.setText("Failed to fetch user data: " + e.getMessage());
                                        tvError.setVisibility(View.VISIBLE);
                                    });
                        }
                    } else {
                        tvError.setText("Login failed: " + task.getException().getMessage());
                        tvError.setVisibility(View.VISIBLE);
                    }
                });
    }

    private void signUpUser(String name, String email, String password) {
        mAuth.createUserWithEmailAndPassword(email, password)
                .addOnCompleteListener(this, task -> {
                    if (task.isSuccessful()) {
                        FirebaseUser user = mAuth.getCurrentUser();
                        if (user != null) {
                            // Save user data to Firebase Database
                            String userId = user.getUid();
                            Map<String, Object> userData = new HashMap<>();
                            userData.put("name", name);
                            userData.put("email", email);
                            userData.put("role", "user"); // Default role, per security rules

                            db.child("users").child(userId).setValue(userData)
                                    .addOnSuccessListener(aVoid -> {
                                        navigateToDomainSetup(email, name);
                                    })
                                    .addOnFailureListener(e -> {
                                        tvError.setText("Failed to save user data: " + e.getMessage());
                                        tvError.setVisibility(View.VISIBLE);
                                        mAuth.signOut(); // Clean up if database write fails
                                    });
                        }
                    } else {
                        tvError.setText("Sign-up failed: " + task.getException().getMessage());
                        tvError.setVisibility(View.VISIBLE);
                    }
                });
    }

    private void navigateToDomainSetup(String email, String name) {
        Intent intent = new Intent(LoginActivity.this, DomainSetupActivity.class);
        intent.putExtra("email", email);
        intent.putExtra("name", name);
        intent.putExtra("testMode", TestConfig.TEST_MODE);
        startActivity(intent);
        finish();
    }
}