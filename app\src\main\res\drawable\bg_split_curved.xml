<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="1080dp"
    android:height="1920dp"
    android:viewportWidth="1080"
    android:viewportHeight="1920">

    <!-- Bottom (dark) fill -->
    <path
        android:fillColor="@color/ColorPrimary"
        android:pathData="M0,0 L1080,0 L1080,1920 L0,1920 Z" />

    <!-- Top (light) with a smooth curved bottom edge -->
    <!-- You can tweak the curve by adjusting the control point (the Q x,y middle). -->
    <path
        android:fillColor="@color/ColorPrimaryLight"
        android:pathData="M0,0 L1080,0 L1080,560
                          Q540,740 0,560 Z" />
</vector>
