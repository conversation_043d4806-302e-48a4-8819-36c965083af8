<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@drawable/bg_split_curved">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingTop="24dp"
            android:paddingBottom="24dp">

                <!-- Domains card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="?attr/colorSurface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

<!--                                        <ImageView-->
<!--                                            android:layout_width="28dp"-->
<!--                                            android:layout_height="28dp"-->
<!--                                            android:src="@drawable/ic_globe"-->
<!--                                            app:tint="@color/ColorSecondary" />-->

                                        <TextView
                                            android:id="@+id/label_domains"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:text="My Domains"
                                            android:textStyle="bold"
                                            android:textSize="18sp"
                                            android:textColor="@color/whiteMore"/>
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_domain"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:text="No domains registered"
                                    android:textSize="16sp"
                                    android:textColor="@color/black"
                                    android:padding="12dp"
                                    android:background="@drawable/edittext_border"/>
                        </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Website card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="?attr/colorSurface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

<!--                                        <ImageView-->
<!--                                            android:layout_width="28dp"-->
<!--                                            android:layout_height="28dp"-->
<!--                                            android:src="@drawable/ic_website"-->
<!--                                            app:tint="@color/ColorSecondary" />-->

                                        <TextView
                                            android:id="@+id/label_websites"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:text="My Websites"
                                            android:textStyle="bold"
                                            android:textSize="18sp"
                                            android:textColor="@color/whiteMore"/>
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_website"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:text="No websites deployed"
                                    android:textSize="16sp"
                                    android:textColor="@color/black"
                                    android:padding="12dp"
                                    android:background="@drawable/edittext_border" />

                                <!-- Actions like Explore grid: two side-by-side buttons -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="12dp"
                                    android:orientation="horizontal">

                                        <com.google.android.material.button.MaterialButton
                                            android:id="@+id/btn_view_site"
                                            style="@style/Widget.OneClick.Button.Filled"
                                            android:layout_width="0dp"
                                            android:layout_height="48dp"
                                            android:layout_weight="1"
                                            android:text="Website" />

                                        <Space
                                            android:layout_width="12dp"
                                            android:layout_height="1dp"/>

                                        <com.google.android.material.button.MaterialButton
                                            android:id="@+id/btn_edit_site"
                                            style="@style/Widget.OneClick.Button.Outlined"
                                            android:layout_width="0dp"
                                            android:layout_height="48dp"
                                            android:layout_weight="1"
                                            android:text="Edit Website" />
                                </LinearLayout>
                        </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Renewal card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="?attr/colorSurface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                        <ImageView
                                            android:layout_width="28dp"
                                            android:layout_height="28dp"
                                            android:src="@drawable/ic_renew"
                                            app:tint="@color/ColorSecondary" />

                                        <TextView
                                            android:id="@+id/label_renewals"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:text="Renewal Reminders"
                                            android:textStyle="bold"
                                            android:textSize="18sp"
                                            android:textColor="@color/whiteMore"/>
                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tv_renewal"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="10dp"
                                    android:text="No renewals due"
                                    android:textSize="16sp"
                                    android:textColor="@color/grayDark"
                                    android:padding="12dp"
                                    android:background="@drawable/edittext_border" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_renew_mpesa"
                                    style="@style/Widget.OneClick.Button.Filled"
                                    android:layout_width="match_parent"
                                    android:layout_height="48dp"
                                    android:layout_marginTop="12dp"
                                    android:text="Renew with M-PESA" />
                        </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <!-- Branded Email card -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardUseCompatPadding="true"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="6dp"
                    app:cardBackgroundColor="?attr/colorSurface">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:padding="16dp">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                        <ImageView
                                            android:layout_width="28dp"
                                            android:layout_height="28dp"
                                            android:src="@drawable/ic_email"
                                            app:tint="@color/ColorSecondary" />

                                        <TextView
                                            android:id="@+id/label_email"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="10dp"
                                            android:text="Branded Email (Premium)"
                                            android:textStyle="bold"
                                            android:textSize="18sp"
                                            android:textColor="@color/whiteMore"/>
                                </LinearLayout>

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_setup_email"
                                    style="@style/Widget.OneClick.Button.Filled"
                                    android:layout_width="match_parent"
                                    android:layout_height="48dp"
                                    android:layout_marginTop="12dp"
                                    android:text="Set Up Business Email" />
                        </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

        </LinearLayout>
</ScrollView>
