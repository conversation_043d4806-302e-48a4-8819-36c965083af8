<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/ColorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="32dp"
        android:paddingBottom="24dp">

        <!-- Title -->
        <TextView
            android:id="@+id/dashboard_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dashboard"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp" />

        <!-- My Domains -->
        <TextView
            android:id="@+id/label_domains"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="My Domains"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_domain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No domains registered"
            android:textSize="16sp"
            android:textColor="@color/grayDark"
            android:padding="12dp"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <!-- My Websites -->
        <TextView
            android:id="@+id/label_websites"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="My Websites"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_website"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No websites deployed"
            android:textSize="16sp"
            android:textColor="@color/grayDark"
            android:padding="12dp"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="12dp" />

        <!-- Make these VISIBLE -->
        <Button
            android:id="@+id/btn_view_site"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="View Live Website"
            android:background="@color/ColorSecondaryDark"
            android:textColor="@color/white"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_edit_site"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Edit Website"
            android:background="@color/ColorSecondaryDark"
            android:textColor="@color/white"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="20dp" />

        <!-- Renewal Reminders -->
        <TextView
            android:id="@+id/label_renewals"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Renewal Reminders"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/tv_renewal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No renewals due"
            android:textSize="16sp"
            android:textColor="@color/grayDark"
            android:padding="12dp"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="12dp" />

        <!-- Make this VISIBLE -->
        <Button
            android:id="@+id/btn_renew_mpesa"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Renew with M-PESA"
            android:background="@color/ColorSecondaryDark"
            android:textColor="@color/white"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="20dp" />

        <!-- Branded Email -->
        <TextView
            android:id="@+id/label_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Branded Email (Premium)"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:layout_marginBottom="8dp" />

        <Button
            android:id="@+id/btn_setup_email"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Set Up Business Email"
            android:background="@color/ColorSecondaryDark"
            android:textColor="@color/white"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="8dp" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="8dp" />
    </LinearLayout>
</ScrollView>
