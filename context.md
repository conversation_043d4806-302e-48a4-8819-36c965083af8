# **Project Context: OneClick AI Website Builder (Hackathon Documentation)**

## 1. Project Overview & Goal

**Project Name:** OneClick
**Core Mission:** To create an all-in-one Android application that empowers non-technical Kenyan entrepreneurs to get a professional online presence in minutes.
**Value Proposition:** "Get your business online in minutes, right from your phone. No code, no complications."

The user flow is centered around a seamless, AI-assisted experience:

1.  **Sign Up/Login:** Fast and simple app authentication.
2.  **AI-Powered Creation:** The user describes their business, and AI suggests domain names and generates a multi-page, styled website.
3.  **Live Preview:** The user sees a real-time preview of their generated website within the app.
4.  **Instant Deployment:** With a final click, the app automates the entire backend process (customer creation, service provisioning, subdomain creation) and deploys the website to a live URL.

## 2. The Unified Technology Stack

The project uses a hybrid architecture, leveraging three core systems for what they do best.

### **System A: The Android Frontend (The User's World)**

- **Technology:** Java, Android SDK
- **Responsibilities:**
  - Handles all User Interface (UI) and User Experience (UX).
  - Manages user authentication (signup/login) by communicating with Firebase Auth.
  - Communicates with the "AI Server" for creative tasks (domain suggestions, logo/site generation).
  - Communicates with the "Hosting Server" for business logic (domain availability checks, final deployment).
  - Displays the real-time website preview fetched from Firebase.

### **System B: The AI & Real-time Backend (The "Creative Brain")**

- **Technology:** Node.js, Express, Firebase Realtime Database, OpenAI API
- **Responsibilities:**
  - **Authentication:** Firebase Auth handles all user identity management.
  - **AI Services:** A Node.js server provides endpoints for:
    - `/v1/domains/suggest`: Suggests available `.ke` domain names based on a user's idea.
    - `/v1/logo/generate`: Generates a business logo using OpenAI's DALL-E.
    - `/v1/sites/generate`: The most critical part. It uses `ejs` templates to generate a complete, multi-page static website (HTML, CSS, JS) based on user input.
  - **Real-time Data Hub:** The Firebase Realtime Database is used to:
    - Store basic user profile data (name, email).
    - Store the generated website's HTML/CSS (`/sites/{firebase_uid}/preview_html`) so the app can fetch it for the live preview.

### **System C: The Hosting & Automation Backend (The "Heavy Lifter")**

- **Technology:** DigitalOcean Droplet (Ubuntu), Apache, PHP, MySQL, WHMCS, Let's Encrypt SSL.
- **Responsibilities:**
  - **WHMCS Platform:** The core engine for managing "customers" and "services" (the websites).
  - **Custom PHP API Layer:** A set of simple PHP scripts that act as the bridge between the app and the complex WHMCS/server logic.
  - **Domain Availability:** Provides a real-time domain check API that leverages WHMCS's Whois capabilities.
  - **Automated Deployment:** The main `create-site.php` API automates the entire deployment process:
    1.  Creates a new customer account in WHMCS invisibly.
    2.  Creates and auto-activates a "free" hosting service for that customer.
    3.  Creates a live, public subdomain (e.g., `mynewsite.oneclick.me.ke`).
    4.  Writes the user's generated HTML to the server, making the site instantly live.
  - **Server Management:** Hosts the main landing page, the WHMCS instance, the API, and all user-generated websites.

## 3. The End-to-End User Flow (Detailed)

This is the exact sequence of events the final app should follow:

1.  **Authentication (`LoginActivity`):** User signs up or logs in via Firebase Auth. The app securely holds the user's **Firebase UID**.
2.  **Domain Setup (`DomainSetupActivity`):**
    - User enters their business idea.
    - App calls the AI Server's `/v1/domains/suggest` endpoint.
    - For each suggestion, the app calls the Hosting Server's `/api/check-domain.php` endpoint to verify availability and get a price.
    - The UI updates to show the list of available domains.
3.  **Website Generation (`DomainSetupActivity`):**
    - User selects a domain and provides info (business name, template style).
    - The app calls the AI Server's `/v1/sites/generate` endpoint.
    - The AI Server generates the HTML, CSS, and JS files and returns them in a JSON object. **(This part is currently broken/not fully implemented in the app).**
4.  **Preview (`PreviewActivity`):**
    - The app should extract the `index.html` content from the AI server's response.
    - It should then start the `PreviewActivity`, passing the HTML content to it via an `Intent` extra.
    - The `PreviewActivity` should load this HTML content into a `WebView` for the user to see. **(This is the second part that is currently broken).**
5.  **Final Deployment (`PreviewActivity`):**
    - The `PreviewActivity` has the final "Publish My Website" button.
    - When clicked, the app makes a `POST` request to the Hosting Server's `/api/create-site.php`.
    - The JSON payload for this request includes the user's details and the full `html_content` that was previewed.
6.  **Confirmation:** The server API returns a success message with the final `live_url`, which the app displays to the user.

## 4. Current Problems to Debug

The core backend infrastructure is complete and tested. The current issues are within the **Android application's logic**, specifically in the handoff between AI generation, previewing, and deployment.

1.  **AI Site Generation is Not Integrated:** The `Generate & Preview Site` button click in `DomainSetupActivity` does not seem to correctly call the AI server's `/v1/sites/generate` endpoint. It appears to be using placeholder/hardcoded HTML instead.
2.  **Preview Flow is Broken:** The app is not successfully passing the generated HTML from the AI service to the `PreviewActivity`. The `PreviewActivity` crashes or fails to load the correct content because it's not receiving the HTML data it needs.
3.  **User Flow Discrepancy:** The app currently has a "Deploy" button on the `DomainSetupActivity` screen, which skips the preview step entirely. This needs to be changed to a "Generate & Preview" button that initiates the correct flow.

## 5. API Contracts (For Reference)

### **AI Server API (hosted at `https://oneclick.me.ke/ai/`)**

- **`POST /v1/sites/generate`**:
  - **Request Body (JSON):** `{"domain": "...", "businessName": "..."}`
  - **Success Response (JSON):** `{"files": {"index.html": "...", "styles.css": "..."}}`

### **Hosting Server API (hosted at `https://oneclick.me.ke/`)**

- **`GET /api/check-domain.php`**:
  - **Query Param:** `?domain=test.co.ke`
  - **Success Response (JSON):** `{"status":"success", "available":true, "price":"..."}`
- **`POST /api/create-site.php`**:
  - **Request Body (JSON):** `{"user": {"name":"...", "email":"...", "password":"..."}, "subdomain":"...", "html_content":"..."}`
  - **Success Response (JSON):** `{"status":"success", "live_url":"..."}`

## 6. Relevant Code Snippets

_(You can now paste the contents of `server/index.cjs` file below this section to provide the AI with the full context of the code it needs to debug.)_

in the folder ai-server-upload, it contains all of Nila's AI services.

also the lib folder contains files of how she tried implementing the services or coding them to work? I dont know but just check them out, might provide good insight. But for context, you should know that only the files in ai-server-upload were the ones uploaded to the server and running on node with a reverse proxy or whatever.
