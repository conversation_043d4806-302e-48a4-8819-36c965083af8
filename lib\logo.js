// lib/logo.js
import placeholder from "./placeholder.png";
import { OPENAI_PROXY } from "./api";

/** Try the server AI logo. Accepts string or {prompt}. */
export async function generateLogo(arg) {
  const prompt = typeof arg === "string" ? arg : arg?.prompt;
  if (!prompt) throw new Error("Missing prompt");

  const res = await fetch(`${OPENAI_PROXY}/v1/logo/generate`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ prompt })
  });

  // 402 from server when OpenAI billing/limit hits
  if (res.status === 402) return ""; // indicate “use fallback”
  const j = await res.json().catch(() => ({}));
  if (!res.ok) throw new Error(j?.error || `Logo failed (HTTP ${res.status})`);
  return j.dataUrl || "";
}

/** Guaranteed data URL: AI first; placeholder on *any* failure. */
export async function getLogoDataUrl(prompt) {
  try {
    const ai = await generateLogo(prompt);
    if (ai) return ai; // data:... or https://...
  } catch (e) {
    // ignore; we’ll fallback
    console.warn("AI logo failed; using placeholder:", e?.message || e);
  }
  // Convert bundled placeholder asset to data URL
  const r = await fetch(placeholder);
  const blob = await r.blob();
  const reader = new FileReader();
  return await new Promise((resolve) => {
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
}
