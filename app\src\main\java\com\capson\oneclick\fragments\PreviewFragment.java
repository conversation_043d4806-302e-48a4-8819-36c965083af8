package com.capson.oneclick.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.capson.oneclick.R;

public class PreviewFragment extends Fragment {

    private String domain;
    private String websiteType;
    private String liveUrl;

    public static PreviewFragment newInstance(@NonNull String domain, @NonNull String websiteType, String email, String emailPass, String liveUrl) {
        PreviewFragment f = new PreviewFragment();
        Bundle args = new Bundle();
        args.putString("domain", domain);
        args.putString("website_type", websiteType);
        if (email != null) args.putString("email", email);
        if (emailPass != null) args.putString("email_pass", emailPass);
        if (liveUrl != null) args.putString("live_url", liveUrl);
        f.setArguments(args);
        return f;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_preview, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View v, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(v, savedInstanceState);

        if (getArguments() != null) {
            domain = getArguments().getString("domain", "");
            websiteType = getArguments().getString("website_type", "");
            liveUrl = getArguments().getString("live_url", "");
        }

        TextView tvDomain = v.findViewById(R.id.tv_domain);
        TextView tvWebsiteType = v.findViewById(R.id.tv_website_type);
        WebView wvPreview = v.findViewById(R.id.wv_preview);

        tvDomain.setText(domain);
        tvWebsiteType.setText(websiteType.isEmpty() ? "Starter" : websiteType);

        if (!liveUrl.isEmpty()) {
            wvPreview.getSettings().setJavaScriptEnabled(true);
            wvPreview.loadUrl(liveUrl);
        } else {
            wvPreview.loadData("<h1>No site available</h1>", "text/html", "UTF-8");
        }
    }
}