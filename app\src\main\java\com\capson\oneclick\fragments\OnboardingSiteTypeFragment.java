package com.capson.oneclick.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.capson.oneclick.R;
import com.google.android.material.card.MaterialCardView;

public class OnboardingSiteTypeFragment extends Fragment {

    public interface Listener {
        void onWebsiteTypeSelected(String type);
    }

    private Listener listener;
    private MaterialCardView selected;

    public OnboardingSiteTypeFragment(Listener listener) {
        this.listener = listener;
    }

    public OnboardingSiteTypeFragment() { }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        View v = inflater.inflate(R.layout.fragment_onboarding_site_type, container, false);

        MaterialCardView cardStore = v.findViewById(R.id.card_store);
        MaterialCardView cardBlog = v.findViewById(R.id.card_blog);
        MaterialCardView cardPortfolio = v.findViewById(R.id.card_portfolio);
        MaterialCardView cardProfile = v.findViewById(R.id.card_profile);

        setup(cardStore, "Online Store");
        setup(cardBlog, "Blog");
        setup(cardPortfolio, "Portfolio");
        setup(cardProfile, "Personal Profile");
        return v;
    }

    private void setup(MaterialCardView card, String label) {
        card.setOnClickListener(view -> {
            select(card);
            if (listener != null) listener.onWebsiteTypeSelected(label);
        });
    }

    private void select(MaterialCardView card) {
        if (selected != null) {
            selected.setStrokeColor(getResources().getColor(R.color.whiteMore));
            selected.setStrokeWidth(2);
        }
        selected = card;
        selected.setStrokeColor(getResources().getColor(R.color.ColorSecondary));
        selected.setStrokeWidth(5);
    }
}
