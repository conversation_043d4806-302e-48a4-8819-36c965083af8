# One-Click .KE Website Builder

## Background
For small businesses, freelancers, creators, and students in Kenya, owning a website is still complicated, slow, and expensive. Many shy away from e-commerce entirely, relying only on social media.  

We want to make setting up a website feel as easy as signing up on Instagram.  
Type your business idea, and our Android app does the rest:  
- Suggests short, brandable **.ke domains**  
- Generates an **instant website preview** on your phone  
- With one **M-PESA tap**, launches the site live and ready to share  

It’s **Kenya-first by design**: English + Swahili, `.ke` domains, and M-PESA built in.  

---
## How the Demo Works
1. Type: “N’s Cakes”  
2. <PERSON> suggests: `nscakes.ke`, `sweetbakes.me.ke`, `caketime.ke` — one marked *Available*  
3. Tap → instant bilingual preview (`EN` ↔ `SW`)  
4. Accept M-PESA push → success  
5. Judges see: *“Your site is live”* + Zip file to download it

---
## Features

### 1. Backend Automations (n8n Flows)
- **User/Business Registration**: Store name, email, phone, business type.  
- **AI Domain Suggestions + WHOIS Check**: Short `.ke` names → availability verified in real-time.  
- **One-Click Deployment**: From JSON template → hosted instantly (Firebase / Netlify / VPS).  
- **M-PESA Integration**: Daraja STK Push → store verified transaction refs.  
- **Reports & Analytics**: Track deployed sites, templates used, and payment trends.  

### 2. Mobile App (Java Android)
- **Onboarding & Login**: Email/Phone + OTP; simple guided setup.  
- **Domain + Website Setup**: Type idea → get AI suggestions → WHOIS check → pay via M-PESA → site goes live.  
- **Site Management**: Dashboard for active domains, renewals, and payments.  
- **Branded Email (Optional)**: Provision addresses like `<EMAIL>`.  
- **Instant Preview**: Auto-filled site rendered instantly on device.  
- **Premium Add-ons**: Themes, e-commerce lite, analytics, logos.  

### Safeguards
- **Domain Conflicts**: Alternatives suggested if taken.  
- **Payment Verification**: Cross-check M-PESA with Daraja response.  
- **Renewals**: Automated expiry reminders with STK prompts.  
---

## Technology Stack
- **Mobile Frontend**: Java (Android Studio)  
- **Backend Automation**: n8n (self-hosted/cloud)  
- **Database**: Firebase Firestore (users + domain records)  
- **Hosting**: Firebase Hosting / Netlify / DigitalOcean VPS  
- **AI**: OpenAI API for domain + content suggestions  
- **Payments**: M-PESA Daraja API (STK Push)  
- **Notifications**: Firebase Cloud Messaging, Twilio/SendGrid (SMS/email)  
---

## Demo Flow (MVP)
**“Go Live in 60 Seconds”**  
1. Sign up with email/phone.  
2. Enter business idea → get AI `.ke` domain suggestions.  
3. WHOIS checks availability.  
4. Pay instantly with M-PESA.  
5. Website goes live on Firebase/Netlify/VPS.  

---
