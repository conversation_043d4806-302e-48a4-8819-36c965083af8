<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#33FFFFFF">

    <item android:id="@android:id/mask">
        <shape android:shape="rectangle">
            <corners android:radius="12dp"/>
            <solid android:color="#FFFFFFFF"/>
        </shape>
    </item>

    <item>
        <selector xmlns:android="http://schemas.android.com/apk/res/android">
            <!-- Disabled -->
            <item android:state_enabled="false">
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="@android:color/transparent"/>
                    <stroke android:width="2dp" android:color="#2E7D32"/> <!-- dimmed green -->
                </shape>
            </item>

            <!-- Pressed (slight tint fill) -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="#14250A"/> <!-- very dark green tint -->
                    <stroke android:width="2dp" android:color="#33C51C"/>
                </shape>
            </item>

            <!-- Default -->
            <item>
                <shape android:shape="rectangle">
                    <corners android:radius="12dp"/>
                    <solid android:color="@android:color/transparent"/>
                    <stroke android:width="2dp" android:color="#33C51C"/> <!-- ColorSecondary -->
                </shape>
            </item>
        </selector>
    </item>
</ripple>
