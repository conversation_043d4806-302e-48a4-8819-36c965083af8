<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/ColorPrimary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingEnd="20dp"
        android:paddingTop="32dp"
        android:paddingBottom="24dp"
        android:gravity="center">

        <ImageView
            android:id="@+id/landing_logo"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:src="@drawable/oneclicklogo"
            android:contentDescription="App Logo" />

        <TextView
            android:id="@+id/login_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Welcome to OneClick"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/ColorSecondary"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/login_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Login or Sign Up"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/whiteMore"
            android:layout_marginBottom="16dp" />

        <EditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="Full Name"
            android:inputType="textPersonName"
            android:padding="12dp"
            android:textColor="@android:color/black"
            android:textColorHint="@color/grayDark"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <EditText
            android:id="@+id/et_email"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="Enter Email"
            android:inputType="textEmailAddress"
            android:padding="12dp"
            android:textColor="@android:color/black"
            android:textColorHint="@color/grayDark"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="Enter Password"
            android:inputType="textPassword"
            android:padding="12dp"
            android:textColor="@android:color/black"
            android:textColorHint="@color/grayDark"
            android:background="@drawable/edittext_border"
            android:layout_marginBottom="16dp" />

        <Button
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Login"
            android:background="@drawable/btn_primary_green"
            android:textColor="@color/button_text_selector"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_sign_up"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Sign Up"
            android:background="@drawable/btn_outline_green"
            android:textColor="@color/button_text_selector"
            android:minHeight="48dp"
            android:paddingVertical="12dp"
            android:paddingHorizontal="16dp"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/tv_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text=""
            android:textColor="#FF4D4D"
            android:textSize="13sp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:visibility="gone" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="8dp" />

    </LinearLayout>
</ScrollView>