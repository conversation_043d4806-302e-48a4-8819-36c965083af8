<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/ColorPrimary"
    android:gravity="center">

<!--    &lt;!&ndash; Toolbar &ndash;&gt;-->
<!--    <androidx.appcompat.widget.Toolbar-->
<!--        android:id="@+id/toolbar"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="?attr/actionBarSize"-->
<!--        android:background="@color/ColorPrimaryDark"-->
<!--        app:title="Email SetUp"-->
<!--        app:titleTextColor="@android:color/white"-->
<!--        app:navigationIcon="@drawable/ic_back_arrow"/>-->

    <ImageView
        android:id="@+id/landing_logo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@drawable/oneclicklogo"
        android:contentDescription="App Logo" />

    <TextView
        android:id="@+id/landing_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="Welcome to OneClick "
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/ColorSecondary" />

    <TextView
        android:id="@+id/landing_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="A one-click website builder that helps any Kenyan instantly get online with a domain and hosting powered by AI automation."
        android:textSize="18sp"
        android:textColor="@color/whiteMore"
        android:gravity="center"
        android:maxWidth="300dp" />

    <Button
        android:id="@+id/btn_get_started"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="Get Started"
        android:background="@drawable/btn_primary_green"
        android:backgroundTint="#4CAF50"
        android:textColor="#FFFFFF" />

    <!--    <Button-->
    <!--        android:id="@+id/btn_login"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="16dp"-->
    <!--        android:text="Login"-->
    <!--        android:backgroundTint="#2196F3"-->
    <!--        android:background="@drawable/btn_primary_green"-->
    <!--        android:textColor="#FFFFFF" />-->


<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:padding="16dp"-->
<!--        android:orientation="vertical">-->
<!--        -->
<!--        -->
<!--    </LinearLayout>-->





</LinearLayout>