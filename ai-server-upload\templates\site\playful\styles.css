:root{
  --gradient-a:#F59E0B;
  --gradient-b:#EC4899;
  --bg:#0f0a12;
  --fg:#fce7f3;
  --accent:#fb7185;
  --panel:#1a1420;
  --border:#2a2030;
}
*{box-sizing:border-box}
html,body{margin:0;padding:0;background:var(--bg);color:var(--fg);font-family:Nunito, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto;line-height:1.55}
a{color:var(--accent);text-decoration:none}
a:hover{text-decoration:underline}
.hero{padding:56px 24px;border-bottom:1px solid var(--border);background:linear-gradient(135deg,var(--gradient-a),var(--gradient-b))}
.brand{display:flex;align-items:center;gap:16px;max-width:1100px;margin:0 auto}
.subtitle{opacity:.9}
.logo{width:72px;height:72px;border-radius:16px;object-fit:cover;box-shadow:0 8px 30px rgba(0,0,0,.25)}
.cta{display:inline-block;margin:24px auto 0;border:1px solid rgba(255,255,255,.6);color:#fff;padding:10px 18px;border-radius:12px;backdrop-filter:blur(2px)}
.grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(260px,1fr));gap:16px;max-width:1100px;margin:24px auto;padding:0 24px 40px}
.card{background:var(--panel);border:1px solid var(--border);border-radius:18px;padding:20px;box-shadow:0 10px 30px rgba(0,0,0,.25)}
.bullets{padding-left:18px}
.page{max-width:900px;margin:32px auto;padding:0 24px}
.footer{border-top:1px solid var(--border);padding:24px;text-align:center;opacity:.8}
