package com.capson.oneclick;

public final class TestConfig {
    private TestConfig() {}

    // ✅ Flip this to false when you want real network calls
    public static final boolean TEST_MODE = true;

    // Nice defaults for auto-fill in demo
    public static final String TEST_IDENTITY = "Small Business (SME)";
    public static final String TEST_WEBSITE_TYPE = "Portfolio";
    public static final String TEST_BUSINESS_NAME = "One Click Biz";
    public static final String TEST_DOMAIN = "oneclick.me.ke";

    // ✅ 1x1 transparent PNG data URL (tiny, always decodes)
    public static final String TEST_LOGO_DATA_URL =
            "data:image/png;base64," +
                    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAA" +
                    "AAC0lEQVR42mP8/58BAgMBApQCFgAAAABJRU5ErkJggg==";
}
