<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_split_curved">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/ColorPrimaryDark"
        app:title="Create Your OneClick Website"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_back_arrow"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp"
            android:orientation="vertical">

            <EditText
                android:id="@+id/et_business_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Enter Your Business Name"
                android:inputType="text"
                android:padding="12dp"
                android:textColor="@color/black"
                android:background="@drawable/edittext_border"
                android:layout_marginBottom="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_suggest_domains"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Suggest .KE Domains"
                style="@style/Widget.OneClick.Button.Filled"
                android:background="@drawable/btn_primary_green"
                android:textColor="#FFFFFF"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/label_logo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="AI Logo (Optional)"
                android:textSize="18sp"
                android:textColor="@color/whiteMore"
                android:layout_marginBottom="8dp" />

            <EditText
                android:id="@+id/et_logo_prompt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Describe your logo (e.g., 'pill + book, purple gradient')"
                android:inputType="text"
                android:padding="12dp"
                android:textColor="@color/black"
                android:background="@drawable/edittext_border"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_generate_logo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Generate Logo"
                style="@style/Widget.OneClick.Button.Filled"
                android:background="@drawable/btn_primary_green"
                android:textColor="#FFFFFF"
                android:layout_marginBottom="16dp" />

            <ImageView
                android:id="@+id/iv_logo_preview"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_gravity="center"
                android:contentDescription="Generated Logo Preview"
                android:visibility="gone"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/label_suggestions"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Suggested Domains"
                android:textSize="18sp"
                android:textColor="@color/whiteMore"
                android:layout_marginBottom="8dp"
                android:visibility="gone" />

            <ListView
                android:id="@+id/lv_domain_suggestions"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                android:scrollbars="vertical" />

            <Button
                android:id="@+id/btn_pay_mpesa"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Pay with M-PESA"
                android:backgroundTint="#2196F3"
                android:textColor="#FFFFFF"
                android:background="@drawable/btn_primary_green"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF0000"
                android:layout_marginTop="8dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/btn_deploy_site"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Generate &amp; Preview Site"
                android:backgroundTint="#4CAF50"
                android:textColor="#FFFFFF"
                android:background="@drawable/btn_primary_green"
                android:visibility="gone" />

        </LinearLayout>
    </ScrollView>
</LinearLayout>