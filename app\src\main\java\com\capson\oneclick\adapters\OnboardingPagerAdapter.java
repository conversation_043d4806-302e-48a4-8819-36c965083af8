package com.capson.oneclick.adapters;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.capson.oneclick.fragments.OnboardingIdentityFragment;
import com.capson.oneclick.fragments.OnboardingSiteTypeFragment;

public class OnboardingPagerAdapter extends FragmentStateAdapter {

    private final Fragment[] pages;

    public OnboardingPagerAdapter(@NonNull FragmentActivity fa,
                                  OnboardingIdentityFragment identity,
                                  OnboardingSiteTypeFragment siteType) {
        super(fa);
        pages = new Fragment[]{ identity, siteType };
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return pages[position];
    }

    @Override
    public int getItemCount() {
        return pages.length;
    }
}
