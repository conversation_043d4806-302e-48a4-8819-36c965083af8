plugins {
    alias(libs.plugins.android.application)
    // Add the Google services Gradle plugin
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.capson.oneclick'
    compileSdk 35

    defaultConfig {
        applicationId "com.capson.oneclick"
        minSdk 24
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.gridlayout
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    implementation 'com.google.android.material:material:1.12.0'
    implementation "com.google.android.material:material:1.12.0"
    implementation "androidx.appcompat:appcompat:1.7.0"
    // Add to app/build.gradle dependencies
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.google.code.gson:gson:2.11.0'

    implementation platform('com.google.firebase:firebase-bom:34.2.0')


    // TODO: Add the dependencies for Firebase products you want to use

    // When using the BoM, don't specify versions in Firebase dependencies

    implementation 'com.google.firebase:firebase-analytics'

    implementation platform('com.google.firebase:firebase-bom:33.4.0')
    implementation 'com.google.firebase:firebase-database' // For Realtime Database
    implementation 'com.google.firebase:firebase-auth' // If using Authentication
    implementation 'com.google.firebase:firebase-storage' // If storing logos
    implementation 'com.google.firebase:firebase-analytics'
    implementation platform('com.google.firebase:firebase-bom:33.2.0')
}