<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_split_curved">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/ColorPrimaryDark"
        app:title="Onboarding"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_back_arrow"/>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <!-- Bottom bar with Back / Next -->
    <LinearLayout
        android:id="@+id/bottomBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_back"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Back"
            android:enabled="false"
            android:background="@drawable/btn_secondary_outline"
            android:textColor="@color/whiteMore" />

        <Space
            android:layout_width="12dp"
            android:layout_height="1dp"/>

        <Button
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="Next"
            android:background="@drawable/btn_primary_green"
            android:textColor="@android:color/white"/>
    </LinearLayout>
</LinearLayout>
