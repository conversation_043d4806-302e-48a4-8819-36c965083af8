<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <!-- Background color of the navigation drawer -->
    <solid android:color="@color/blueAccent"/>

    <!-- Use this if you want ALL corners rounded -->
    <!-- <corners android:radius="40dp"/> -->

    <!-- Instead of topEndRadius and bottomEndRadius, use these -->
    <corners android:topRightRadius="60dp"
        android:bottomRightRadius="60dp"/>

    <!-- Optional: Add padding for a shadow effect -->
    <padding android:left="5dp"/>

<!--    <solid android:color="#E6202441"/> &lt;!&ndash; 90% Transparent Blue &ndash;&gt;-->

</shape>
