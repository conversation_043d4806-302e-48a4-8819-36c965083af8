﻿# OneClick Expo MVP — Architecture, Audit, and Build Plan

## 1) Integration Audit — Current Repo and Gaps

This section summarizes what’s in the codebase, how parts connect today, and what likely needs attention to get a fully working flow.

### Codebase Inventory
- Android app (Java): `app/src/main/java/com/capson/oneclick/...`
  - Auth via Firebase Auth; data writes to Firebase Realtime Database.
  - Key flows in `DomainSetupActivity` and `PreviewActivity` call the AI server and the hosting server.
- AI server (Node/Express): `ai-server-upload/server/index.cjs` (prod entry) and `index.js` (ESM version)
  - Endpoints:
    - `POST /v1/domains/suggest`: Suggests .ke/.co.ke/.me.ke.
    - `POST /v1/logo/generate`: Uses OpenAI Images API (returns base64 data URL or remote URL).
    - `POST /v1/sites/generate`: Generates a multi-page static site from EJS templates; optional ZIP.
  - Templates: `ai-server-upload/templates/site/<style>/{index,about,contact,products}.ejs` with CSS/JS per style.
- Client helper JS (Expo-oriented): `lib/` directory
  - API wrappers intended for an Expo app (`lib/api.js`, `lib/ai.js`, `lib/logo.js`, `lib/availability.js`, `lib/zip.js`).

### What’s Deployed vs. What’s Needed
- You uploaded only `ai-server-upload` and run its server behind a reverse proxy at `/ai/`.
  - This is sufficient for the AI endpoints, with one important caveat below.
- Hosting server PHP APIs (not in this repo) are assumed present:
  - `GET https://oneclick.me.ke/api/check-domain.php`
  - `POST https://oneclick.me.ke/api/create-site.php`

### Integration Findings and Likely Issues
- Templates path resolution on server:
  - `server/index.cjs` uses `path.resolve(process.cwd(), 'templates', 'site', style)`.
  - If you start the server from `ai-server-upload/server`, `process.cwd()` is `server/`, so it will look for `server/templates/site/...` which doesn’t exist. In that case, the code falls back to default HTML/CSS instead of rendering EJS templates.
  - Fix: run the server with CWD at `ai-server-upload` root, or change code to `path.resolve(__dirname, '../templates', 'site', style)`.
- Android → AI base URL concatenation has a double slash:
  - `CORE_BASE = "https://oneclick.me.ke/ai/"` then calls `CORE_BASE + "/v1/sites/generate"` => `.../ai//v1/sites/generate`.
  - Most proxies tolerate this, but it’s brittle. Use `CORE_BASE + "v1/sites/generate"`.
- Android logo rendering assumes base64 only:
  - AI server may return a remote URL (not a `data:image/png;base64,...`). The Android code tries to base64 decode unconditionally, causing display failures.
  - Fix: if it’s a `data:` URL, decode; if it’s `http`/`https`, load with an image loader (e.g., Glide) or `WebView`.
- AI server does not write preview to Firebase:
  - The context’s “real-time preview via Firebase” isn’t implemented in `index.cjs`. The Android app previews the HTML directly via Intent extras. This is fine for MVP; just a discrepancy with the original design.

### Bottom Line
- The server folder you deployed is enough for AI endpoints, provided:
  - You run with correct working directory (or update path resolution) so templates are found.
  - You set `OPENAI_API_KEY` in the server environment.
  - Reverse proxy `https://oneclick.me.ke/ai/` to the Node process.
- Android app mostly integrates the intended flow now, but the three issues above can explain prior failures:
  1) Templates not found due to `process.cwd()`.
  2) Double slash in AI URL.
  3) Logo decode failure when server returns a remote URL.

---

## 2) Recommended Tech Stack (Expo MVP)

- App framework: Expo (SDK 51+) with Expo Router (file-based navigation).
- Language: TypeScript.
- UI: NativeWind (Tailwind for RN) or React Native Paper for consistent components.
- Forms: React Hook Form + Zod (schema validation).
- Data fetching: TanStack Query (React Query) for API/cache lifecycle.
- Global state: Zustand (lightweight, predictable) for session + builder state.
- Auth/DB:
  - Option A (keep momentum): Firebase Auth + Firebase Realtime Database/Firestore.
  - Option B (alternative): Supabase Auth + Postgres + Realtime if you want SQL + row-level security.
- File export/share: `expo-file-system`, `expo-sharing` (already mirrored by `lib/zip.js`).
- Analytics: PostHog or Firebase Analytics.
- Environment/Config: `app.config.ts` or `expo-env` + `expo-constants` for runtime-read extras.
- HTTP: Fetch with `fetch` and well-typed API layer (wrapping `lib/api.ts`).

Firebase vs Supabase: For this MVP, reusing Firebase is faster (matches current infra and auth). Supabase is a good future option for SQL + policies + realtime, but would add migration overhead now. Recommendation: Firebase (Option A) for MVP; re-evaluate Supabase post-MVP.

---

## 3) Proposed File & Folder Structure

```
oneclick-expo/
  app/
    (auth)/
      login.tsx
      signup.tsx
    (onboarding)/
      index.tsx
      identity.tsx
      site-type.tsx
    (builder)/
      domain.tsx
      logo.tsx
      template.tsx
      preview.tsx
      publish.tsx
    (dashboard)/
      index.tsx
      settings.tsx
    _layout.tsx
    index.tsx    # Splash/entry; routes to (auth) or (dashboard)
  src/
    api/
      client.ts        # base fetch, interceptors
      ai.ts            # /ai endpoints (domains, logo, sites)
      hosting.ts       # /api endpoints (check-domain, create-site)
      firebase.ts      # auth/db initialization
    components/
      Button.tsx
      TextField.tsx
      DomainCard.tsx
      TemplatePicker.tsx
      LogoPreview.tsx
      WebPreview.tsx   # WebView wrapper
    hooks/
      useAuth.ts
      useDebounce.ts
    state/
      auth.store.ts    # user, session
      builder.store.ts # domain, businessName, logo, template, files
    utils/
      types.ts
      validators.ts
      formatting.ts
    lib/
      availability.ts  # port from repo/lib
      ai.ts            # port from repo/lib
      api.ts           # port from repo/lib
      logo.ts          # port from repo/lib
      zip.ts           # port from repo/lib
  app.config.ts        # expo config with extras for API bases
  package.json
  tsconfig.json
  .env (local only) / .env.example
  README.md
```

---

## 4) Screen-by-Screen Component Breakdown

Below: purpose, primary UI, local/global state, and API calls per screen.

### Splash (app/index.tsx)
- Purpose: Decide route (logged-in -> Dashboard, else -> Auth).
- UI: Minimal loader.
- State: Reads `auth.store` for session.
- API: None.

### Login (app/(auth)/login.tsx)
- Purpose: Email/password login.
- UI: Email field, password field, Login button, link to Sign Up.
- State: Local form via React Hook Form; updates `auth.store` on success.
- API:
  - Firebase Auth: `signInWithEmailAndPassword`.

### Signup (app/(auth)/signup.tsx)
- Purpose: Create account, then redirect into onboarding.
- UI: Name, email, password; Sign up button.
- State: Local form; on success, write user profile to DB; set `auth.store`.
- API:
  - Firebase Auth: `createUserWithEmailAndPassword`.
  - Firebase DB: save `{name,email,role}` under `/users/{uid}`.

### Onboarding: Identity (app/(onboarding)/identity.tsx)
- Purpose: Capture audience/identity for better suggestions.
- UI: Radio or pill selector.
- State: Write to `builder.store.identity`.
- API: None; advance to next onboarding step.

### Onboarding: Site Type (app/(onboarding)/site-type.tsx)
- Purpose: Choose Portfolio/Store/Blog/etc.
- UI: Cards list with descriptions.
- State: `builder.store.siteType`.
- API: None; proceed to builder.

### Domain Setup (app/(builder)/domain.tsx)
- Purpose: Suggest and check availability.
- UI: Input for business/idea; button “Suggest Domains”; list of suggestions; availability badges; select one.
- State: `builder.store.businessName`, `builder.store.selectedDomain`.
- API:
  - AI: `POST /ai/v1/domains/suggest` with `{idea, locale:'en-KE', bilingual:true, audience}`.
  - Hosting: `GET /api/check-domain.php?domain=...` for each suggestion (batch with Promise.all).

### Logo (app/(builder)/logo.tsx)
- Purpose: Generate logo via AI and preview.
- UI: Prompt field (prefill from businessName); “Generate Logo”; preview image.
- State: `builder.store.logoDataUrl`.
- API:
  - AI: `POST /ai/v1/logo/generate` with `{prompt}`; handle both data URLs and remote URLs.

### Template (app/(builder)/template.tsx)
- Purpose: Pick a style (“colorful”, “cool”, “minimal”, “sleek”, “playful”).
- UI: Style cards with subtle previews.
- State: `builder.store.templateStyle`.
- API: None.

### Preview (app/(builder)/preview.tsx)
- Purpose: Build site; show live preview of HTML.
- UI: WebView rendering `files['index.html']`; “Edit Info” and “Publish” buttons; “Download ZIP”.
- State: `builder.store.files` object (HTML/CSS/JS), `builder.store.htmlContent` for index.
- API:
  - AI: `POST /ai/v1/sites/generate` with `{domain, businessName, templateStyle, logoDataUrl}` -> `{files:{...}}`.
  - Optional: `exportZip(files)` via `expo-file-system` + `jszip`.

### Publish (app/(builder)/publish.tsx)
- Purpose: Final deploy to live subdomain.
- UI: Confirmation, spinner, success URL.
- State: Uses `auth.store.user` + `builder.store.{domain,htmlContent}`.
- API:
  - Hosting: `POST /api/create-site.php` body `{ user:{name,email,password}, subdomain, html_content }`.
  - On success, write to `/sites/{domain}` in Firebase DB.

### Dashboard (app/(dashboard)/index.tsx)
- Purpose: List owned sites; quick actions to view/manage.
- UI: Cards per site with live URL and status.
- State: Reads from Firebase `/sites` by `uid`.
- API: Firebase DB listener or query.

### Settings/Profile (app/(dashboard)/settings.tsx)
- Purpose: Profile info, sign out.
- UI: Profile fields, Sign out button.
- State: Reads/writes user profile; `auth.store.signOut()`.
- API: Firebase Auth signOut; optional DB write for profile updates.

---

## 5) Global State Management

Use Zustand with two slices:
- `auth.store.ts`
  - `user`: `{ uid, email, name } | null`
  - `status`: `'idle' | 'loading' | 'authenticated'`
  - `actions`: `signIn(email,pwd)`, `signUp(name,email,pwd)`, `signOut()`
- `builder.store.ts`
  - `businessName`: string
  - `identity`: string | null
  - `siteType`: 'Portfolio' | 'Store' | 'Blog' | 'Profile'
  - `selectedDomain`: string | null
  - `templateStyle`: 'colorful'|'cool'|'minimal'|'sleek'|'playful'
  - `logoDataUrl`: string | null  // may be data: URL or https URL
  - `files`: Record<string,string>  // HTML/CSS/JS from AI server
  - `htmlContent`: string           // files['index.html']
  - `actions`: setters + `reset()`

Server data interactions (AI/hosting) live in `src/api/*` and are consumed with React Query hooks for retries, caching, and loading states. Zustand holds the “builder wizard” state across screens; React Query handles the network lifecycle.

---

## 6) Step-by-Step Development Plan

1. Scaffold project
   - `npx create-expo-app -t expo-router` (TypeScript)
   - Add dependencies: `zustand`, `@tanstack/react-query`, `react-hook-form`, `zod`, `nativewind` or `react-native-paper`, `expo-web-browser`, `expo-constants`, `expo-file-system`, `expo-sharing`, `react-native-webview`, `jszip`.
   - Configure NativeWind or Paper theme.
2. Configure environment and config
   - Add `app.config.ts` with `extra` values:
     - `EXPO_PUBLIC_PROXY_URL` -> `https://oneclick.me.ke/ai`
     - `EXPO_PUBLIC_AVAILABILITY_API_BASE` -> `https://oneclick.me.ke/api`
     - `EXPO_PUBLIC_OPENAI_MODEL` -> `gpt-4o-mini` (or server default)
   - Create `.env.example` for local overrides.
3. Firebase setup (Option A)
   - Add Firebase JS SDK (modular). Initialize in `src/api/firebase.ts` from `.env`/`extra`.
   - Implement `src/hooks/useAuth.ts` wrappers for sign in/up/out.
   - Create Realtime Database or Firestore structure:
     - `/users/{uid}`: `{name,email,role}`
     - `/sites/{domainKey}`: `{uId,domain,liveUrl,websiteType,businessName,logoUrl,createdAt}`
4. API layer
   - Port and adapt `lib/api.js`, `lib/ai.js`, `lib/logo.js`, `lib/availability.js`, `lib/zip.js` into `src/lib/` and `src/api/` TypeScript modules.
   - Ensure logo handler supports both `data:` and `http(s)` URLs.
   - Add hosting server functions: `checkDomain(domain)`, `createSite(payload)`.
5. Navigation and skeleton screens
   - Implement `(auth)` and `(dashboard)` stacks with Expo Router; Splash routes by `auth.store.user`.
   - Create builder screens and wire `builder.store` state transitions.
6. Domain suggestions + availability
   - Hook up Suggest → show list → parallel availability checks → select domain.
7. Logo generation
   - Generate logo (data URL or external URL) and preview in UI.
8. Site generation + preview
   - Call `POST /ai/v1/sites/generate`; store `files`; render `files['index.html']` via `WebView`.
   - Add “Download ZIP” using `exportZip(files)`.
9. Publish flow
   - Call `POST /api/create-site.php` with `{ user, subdomain, html_content }` from state.
   - On success, write site record to Firebase.
10. Dashboard & settings
    - List sites for current user; add basic settings and sign out.
11. Polishing
    - Loading/error states (React Query), form validation (Zod), empty states, toasts.
    - Basic analytics (screen views, publish events).
12. Build & QA
    - EAS build for Android; smoke test flows on device.

---

## 7) Server-Side Checklist (to unblock AI features)

- AI server
  - Run with CWD where `templates/site` exists relative to the process:
    - Option 1: start from `ai-server-upload` root: `node server/index.cjs`
    - Option 2: change `renderSite` root to `path.resolve(__dirname, '../templates/site', style)`
  - Ensure `OPENAI_API_KEY` is set. Check `/v1/health`.
  - Reverse proxy `/ai/` correctly (no double slashes needed).
- Hosting server
  - Confirm `/api/check-domain.php` and `/api/create-site.php` are reachable from the app domain.
- Android app follow-ups (if continuing to use)
  - Fix CORE_BASE concatenation (remove extra `/`).
  - Handle logo URLs that are not base64 data URLs (use Glide or similar).

---

## 8) Why Keep Firebase for MVP?

- You already have Firebase Auth/DB wired in Android; Expo can reuse the same project.
- Minimal backend changes; focus remains on UX and the AI + hosting integration.
- Supabase offers strong Realtime/Postgres features, but migration adds auth logic, schema design, RLS policies, and data migration. It’s great post-MVP.

---

## 9) Notes on Security and UX

- Never expose server secrets in the app; the AI server holds `OPENAI_API_KEY`.
- Validate and sanitize all inputs to AI endpoints.
- For publishing, display clear confirmation with final `live_url` and offer “Share”/“Copy Link”.
- Persist partially-completed builder state locally (e.g., AsyncStorage) to protect against app restarts.
- Add modest rate limiting on AI server; use CORS to allow only your app domains.

---

## 10) Quick Task Backlog (MVP)

- Fix server template path (CWD or `__dirname`).
- Confirm reverse proxy to `/ai/` and set `OPENAI_API_KEY`.
- Build Expo app scaffold with routes and state slices.
- Implement domain suggestion + availability UI.
- Implement logo generation UI supporting both data URL and remote URL.
- Implement site generation preview (WebView) and ZIP export.
- Implement publish to hosting server and DB write.
- Add a simple dashboard of published sites.
- QA on low-end Android phones; measure time-to-first-preview and optimize.

---

If you want, I can proceed to generate the Expo project scaffolding and stub the API modules to match your endpoints. I can also update the Node server to resolve templates via `__dirname` so it’s resilient to working directory differences.
