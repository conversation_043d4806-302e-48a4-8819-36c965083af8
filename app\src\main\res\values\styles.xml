<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="SelectableCard">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">12dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="strokeColor">@color/whiteMore</item>
        <item name="strokeWidth">1dp</item>
        <item name="rippleColor">@color/whiteMore</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <style name="SelectableCardSelected">
        <item name="strokeColor">@color/ColorSecondary</item>
        <item name="strokeWidth">2dp</item>
    </style>

    <style name="SelectableCardContent">
        <item name="android:padding">16dp</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="SelectableCardText">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_marginStart">12dp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textSize">16sp</item>
    </style>
</resources>
