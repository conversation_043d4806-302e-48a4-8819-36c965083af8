package com.capson.oneclick.activities;  // Replace with your actual package name

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.capson.oneclick.R;

public class EmailSetupActivity extends AppCompatActivity {

    private TextView tvDomain, tvPreviewEmail, tvStatus;
    private EditText etEmailPrefix;
    private Button btnPayMpesa, btnSetupEmail;
    private String domain;
    private boolean isPremiumPaid = false;  // Simulate premium status

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_email_setup);


        // Toolbar setup
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());

        tvDomain = findViewById(R.id.tv_domain);
        tvPreviewEmail = findViewById(R.id.tv_preview_email);
        tvStatus = findViewById(R.id.tv_status);
        etEmailPrefix = findViewById(R.id.et_email_prefix);
        btnPayMpesa = findViewById(R.id.btn_pay_mpesa);
        btnSetupEmail = findViewById(R.id.btn_setup_email);

        // Get data from DashboardActivity
        Intent intent = getIntent();
        domain = intent.getStringExtra("domain");

        // Update UI with domain
        if (domain != null && !domain.isEmpty()) {
            tvDomain.setText("Domain: " + domain);
            tvPreviewEmail.setText("Your email: prefix@" + domain);
        } else {
            tvDomain.setText("No domain selected");
            tvStatus.setText("Error: No domain available for email setup");
            tvStatus.setVisibility(View.VISIBLE);
            return;
        }

        // Update email preview as user types
        etEmailPrefix.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                String prefix = s.toString().trim();
                tvPreviewEmail.setText("Your email: " + (prefix.isEmpty() ? "prefix" : prefix) + "@" + domain);
            }
        });

        btnPayMpesa.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Simulate M-PESA payment for premium feature
                if (processMpesaPayment(domain)) {
                    tvStatus.setText("Payment successful! You can now set up your email.");
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                    tvStatus.setVisibility(View.VISIBLE);
                    isPremiumPaid = true;
                    btnSetupEmail.setEnabled(true);
                    btnPayMpesa.setVisibility(View.GONE);
                } else {
                    tvStatus.setText("Payment failed. Please try again.");
                    tvStatus.setVisibility(View.VISIBLE);
                }
            }
        });

        btnSetupEmail.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String prefix = etEmailPrefix.getText().toString().trim();
                if (prefix.isEmpty()) {
                    tvStatus.setText("Please enter an email prefix");
                    tvStatus.setVisibility(View.VISIBLE);
                    return;
                }

                String fullEmail = prefix + "@" + domain;

                // Set up branded email
                if (setupBrandedEmail(fullEmail)) {
                    tvStatus.setText("Email setup successful: " + fullEmail);
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                    tvStatus.setVisibility(View.VISIBLE);
                    // Return to Dashboard
                    Intent dashboardIntent = new Intent(EmailSetupActivity.this, DashboardActivity.class);
                    dashboardIntent.putExtra("domain", domain);
                    startActivity(dashboardIntent);
                    finish();
                } else {
                    tvStatus.setText("Failed to set up email. Try again.");
                    tvStatus.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    private boolean processMpesaPayment(String domain) {
        // TODO: Replace with actual M-PESA API integration (e.g., Safaricom Daraja API)
        // Simulate successful payment
        return true;
    }

    private boolean setupBrandedEmail(String fullEmail) {
        // TODO: Replace with actual email provider API integration (e.g., Zoho, Google Workspace)
        // Simulate successful setup
        return true;
    }
}