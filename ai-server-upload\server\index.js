import express from 'express';
import cors from 'cors';
import { config } from 'dotenv';
import { OpenAI } from 'openai';
import ejs from 'ejs';
import archiver from 'archiver';
import path from 'node:path';
import fs from 'node:fs';
import { PassThrough } from 'node:stream';

config();
const app = express();
app.use(cors());
app.use(express.json());

const PORT = process.env.PORT || 8788;
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

const STYLES = ['colorful','cool','minimal','sleek','playful'];

function renderSite(style, data) {
  const root = path.resolve(process.cwd(), 'templates', style);
  const out = {};
  const pages = ['index','about','contact','products'];
  for (const p of pages) {
    const file = path.join(root, `${p}.ejs`);
    const str = fs.existsSync(file) ? fs.readFileSync(file, 'utf8') : '<h1>Missing template</h1>';
    out[`${p}.html`] = ejs.render(str, data, { async: false });
  }
  const readIf = f => (fs.existsSync(f) ? fs.readFileSync(f, 'utf8') : '');
  out['styles.css'] = readIf(path.join(root, 'styles.css'));
  out['script.js']  = readIf(path.join(root, 'script.js'));
  return out;
}

function zipBundle(files) {
  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } });
    const sink = new PassThrough();
    const chunks = [];
    sink.on('data', d => chunks.push(Buffer.from(d)));
    sink.on('end', () => resolve(Buffer.concat(chunks)));
    sink.on('error', reject);
    archive.on('error', reject);
    archive.pipe(sink);
    for (const [name, content] of Object.entries(files)) archive.append(content, { name });
    archive.finalize().catch(reject);
  });
}

app.get('/v1/health', (req,res) => res.json({ ok:true }));

app.post('/v1/domains/suggest', (req,res) => {
  const { idea, bilingual=true, locale='en-KE' } = req.body || {};
  if (!idea) return res.status(400).json({ error:{ code:'bad_request', message:'Required: idea' }});
  const base = idea.toLowerCase().replace(/[^a-z0-9 ]/g,' ').split(/\s+/).filter(Boolean);
  const picks = Array.from(new Set([
    base.slice(0,2).join(''),
    base.slice(-2).join(''),
    base.find(w => w.length > 6) || base[0] || 'brand',
    'swift','nova','spark','hub','prime','keen','pharm','uni'
  ])).slice(0,8);
  const zones = ['.co.ke','.me.ke','.ke'];
  const domainIdeas = [];
  for (const p of picks) for (const z of zones) domainIdeas.push((p+z).replace(/\s+/g,''));
  res.json({ domainIdeas: Array.from(new Set(domainIdeas)).slice(0,12), bilingual, locale });
});

app.post('/v1/sites/generate', async (req,res) => {
  const { domain, businessName, templateStyle='colorful', asZip=false, logoDataUrl } = req.body || {};
  if (!domain || !businessName) return res.status(400).json({ error:{ code:'bad_request', message:'Required: domain, businessName' }});
  const style = STYLES.includes(templateStyle) ? templateStyle : 'colorful';
  const files = renderSite(style, { domain, businessName, logoDataUrl });
  if (asZip) {
    const zip = await zipBundle(files);
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${domain}.zip"`);
    return res.send(zip);
  }
  res.json({ files });
});

app.post('/v1/logo/generate', async (req,res) => {
  try {
    const { prompt } = req.body || {};
    if (!prompt) return res.status(400).json({ error:'Missing prompt' });
    if (!process.env.OPENAI_API_KEY) return res.status(500).json({ error:'Missing OPENAI_API_KEY' });
    const r = await openai.images.generate({
      model: 'gpt-image-1',
      prompt: `${prompt}. Clean vector-style logomark on neutral background, modern and simple.`,
      size: '512x512',
      response_format: 'b64_json'
    });
    const b64 = r.data?.[0]?.b64_json;
    if (!b64) return res.status(500).json({ error:'No image returned' });
    res.json({ dataUrl: `data:image/png;base64,${b64}` });
  } catch (e) {
    res.status(500).json({ error: e?.message || 'logo_failed' });
  }
});

app.listen(PORT, () => console.log(`Server on http://127.0.0.1:${PORT}`));
