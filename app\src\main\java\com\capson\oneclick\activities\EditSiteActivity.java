package com.capson.oneclick.activities;  // Replace with your actual package name

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.capson.oneclick.R;
import com.capson.oneclick.fragments.DashboardFragment;

public class EditSiteActivity extends AppCompatActivity {

    private TextView tvDomain, tvStatus;
    private EditText etSiteTitle, etDescription, etHeroImage;
    private Button btnPreviewChanges, btnSaveChanges;
    private String domain, websiteType;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_site);


        // Toolbar setup
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setNavigationOnClickListener(v -> finish());

        tvDomain = findViewById(R.id.tv_domain);
        tvStatus = findViewById(R.id.tv_status);
        etSiteTitle = findViewById(R.id.et_site_title);
        etDescription = findViewById(R.id.et_description);
        etHeroImage = findViewById(R.id.et_hero_image);
        btnPreviewChanges = findViewById(R.id.btn_preview_changes);
        btnSaveChanges = findViewById(R.id.btn_save_changes);

        // Get data from PreviewActivity or DashboardActivity
        Intent intent = getIntent();
        domain = intent.getStringExtra("domain");
        websiteType = intent.getStringExtra("website_type");

        // Update UI with domain
        if (domain != null && !domain.isEmpty()) {
            tvDomain.setText("Editing: " + domain);
            // Load existing content (simulated)
            loadExistingContent();
        } else {
            tvDomain.setText("No domain selected");
            tvStatus.setText("Error: No domain available for editing");
            tvStatus.setVisibility(View.VISIBLE);
            return;
        }

        btnPreviewChanges.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Validate inputs
                String siteTitle = etSiteTitle.getText().toString().trim();
                String description = etDescription.getText().toString().trim();
                String heroImage = etHeroImage.getText().toString().trim();

                if (siteTitle.isEmpty() || description.isEmpty()) {
                    tvStatus.setText("Please fill in the title and description");
                    tvStatus.setVisibility(View.VISIBLE);
                    return;
                }

                // Navigate to PreviewActivity with updated content
                Intent previewIntent = new Intent(EditSiteActivity.this, PreviewActivity.class);
                previewIntent.putExtra("domain", domain);
                previewIntent.putExtra("website_type", websiteType);
                previewIntent.putExtra("site_title", siteTitle);
                previewIntent.putExtra("description", description);
                previewIntent.putExtra("hero_image", heroImage);
                startActivity(previewIntent);
            }
        });

        btnSaveChanges.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Validate inputs
                String siteTitle = etSiteTitle.getText().toString().trim();
                String description = etDescription.getText().toString().trim();
                String heroImage = etHeroImage.getText().toString().trim();

                if (siteTitle.isEmpty() || description.isEmpty()) {
                    tvStatus.setText("Please fill in the title and description");
                    tvStatus.setVisibility(View.VISIBLE);
                    return;
                }

                // Save changes to the website
                if (saveWebsiteContent(domain, websiteType, siteTitle, description, heroImage)) {
                    tvStatus.setText("Changes saved successfully!");
                    tvStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
                    tvStatus.setVisibility(View.VISIBLE);
                    // Return to Dashboard
                    Intent dashboardIntent = new Intent(EditSiteActivity.this, DashboardFragment.class);
                    dashboardIntent.putExtra("domain", domain);
                    dashboardIntent.putExtra("website_type", websiteType);
                    startActivity(dashboardIntent);
                    finish();
                } else {
                    tvStatus.setText("Failed to save changes. Try again.");
                    tvStatus.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    private void loadExistingContent() {
        // TODO: Replace with actual API call to fetch existing website content
        // Simulate loading dummy content based on website type
        switch (websiteType.toLowerCase()) {
            case "online store":
                etSiteTitle.setText("My Online Store");
                etDescription.setText("Welcome to our store! Shop the best products in Kenya.");
                etHeroImage.setText("https://example.com/store-hero.jpg");
                break;
            case "blog":
                etSiteTitle.setText("My Blog");
                etDescription.setText("Read my latest posts about life, culture, and more.");
                etHeroImage.setText("https://example.com/blog-hero.jpg");
                break;
            case "portfolio":
                etSiteTitle.setText("My Portfolio");
                etDescription.setText("Showcasing my creative work and projects.");
                etHeroImage.setText("https://example.com/portfolio-hero.jpg");
                break;
            default:
                etSiteTitle.setText("My Profile");
                etDescription.setText("Learn more about me and my journey.");
                etHeroImage.setText("https://example.com/profile-hero.jpg");
                break;
        }
    }

    private boolean saveWebsiteContent(String domain, String websiteType, String siteTitle, String description, String heroImage) {
        // TODO: Replace with actual API call to save website content to hosting provider
        // Simulate successful save
        return true;
    }
}