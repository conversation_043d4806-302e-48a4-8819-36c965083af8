package com.capson.oneclick.activities;  // Replace with your actual package name

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.capson.oneclick.R;

public class LandingActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_landing);  // Assuming the XML is named activity_landing.xml

        Button btnGetStarted = findViewById(R.id.btn_get_started);
//        Button btnLogin = findViewById(R.id.btn_login);

        btnGetStarted.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Navigate to Sign Up or Onboarding screen (next in flow: Login & Setup)
                Intent intent = new Intent(LandingActivity.this, LoginActivity.class);  // Placeholder for next activity
                startActivity(intent);
            }
        });

//        btnLogin.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // Navigate to Login screen
//                Intent intent = new Intent(LandingActivity.this, LoginActivity.class);  // Placeholder for login activity
//                startActivity(intent);
//            }
//        });
    }
}