<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@drawable/bg_split_curved">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/ColorPrimaryDark"
        app:title="Website Preview"
        app:titleTextColor="@android:color/white"
        app:navigationIcon="@drawable/ic_back_arrow"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp"
        android:orientation="vertical">


<!--    <TextView-->
<!--        android:id="@+id/preview_title"-->
<!--        android:layout_width="wrap_content"-->
<!--        android:layout_height="wrap_content"-->
<!--        android:text="Your .KE Website Preview"-->
<!--        android:textSize="24sp"-->
<!--        android:textStyle="bold"-->
<!--        android:textColor="@color/ColorSecondary"-->
<!--        android:layout_marginBottom="16dp" />-->

    <TextView
        android:id="@+id/tv_domain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Loading your website..."
        android:textSize="18sp"
        android:textColor="@color/whiteMore"
        android:layout_marginBottom="8dp" />

    <WebView
        android:id="@+id/webview_preview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="16dp" />

    <Button
        android:id="@+id/btn_edit_site"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Edit Website"
        android:backgroundTint="#2196F3"
        android:textColor="#FFFFFF"
        android:background="@drawable/btn_primary_green"
        android:layout_marginBottom="16dp" />
    <Button
        android:id="@+id/btn_launch_site"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Launch Website"
        android:backgroundTint="#4CAF50"
        android:background="@drawable/btn_primary_green"
        android:textColor="#FFFFFF" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#FF0000"
        android:layout_marginTop="8dp"
        android:visibility="gone" />






    </LinearLayout>


</LinearLayout>
